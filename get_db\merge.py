import json
import os

floats = {
    "Factory New": [0,  0.07],
    "Minimal Wear": [0.6999, 0.15],
    "Field-Tested": [0.14999, 0.37],
    "Well-Worn": [0.36999, 0.44],
    "Battle-Scarred": [0.43999, 1.00]
}
def getWearsFromFloatRange(floatRange):
    wears = []
    for wear, wearRange in floats.items():
        if floatRange[0] <= wearRange[0] and floatRange[1] >= wearRange[1]:
            wears.append(wear)
    return wears

if os.path.exists("csgoskinsgg.json") and os.path.exists("out_float_range_final.json"):
    pass
else:
    print("csgoskinsgg.json and out_float_range_final.json must be in the same directory as this script")
    exit()

with open("csgoskinsgg.json", "r") as f:
    out_final = json.load(f)

with open("out_float_range_final.json", "r") as f:
    out_float_range_final = json.load(f)

for collection in out_final:
    print(collection)
    for rarity in out_final[collection]:
        print(f"---{rarity}")
        for skin in out_final[collection][rarity]:
            out_final[collection][rarity][skin]["wearRange"] = out_float_range_final[skin.replace("StatTrak™ ", "").replace("Souvenir ", "")]
            out_final[collection][rarity][skin]["wears"] = getWearsFromFloatRange(out_final[collection][rarity][skin]["wearRange"])
            out_final[collection][rarity][skin]["prices"] = {}
            for wear in out_final[collection][rarity][skin]["wears"]:
                out_final[collection][rarity][skin]["prices"][wear] = 0

with open("db_csgoskinsgg.json", "w") as f:
    json.dump(out_final, f, indent=4)