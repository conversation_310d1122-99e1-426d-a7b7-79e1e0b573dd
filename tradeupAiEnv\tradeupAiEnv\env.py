import gymnasium as gym
from gymnasium import spaces
import numpy as np

class SkinPickEnv(gym.Env):
    def __init__(self, picks=10):
        super().__init__()
        self.picks = picks
        self.items = []
        self.action_space = None  # delay until reset

        self.observation_space = spaces.Dict({
            "ids": spaces.Sequence(spaces.Text(max_length=50)),
            "wears": spaces.Sequence(spaces.Box(0.0, 1.0, shape=())),
            "collections": spaces.Sequence(spaces.Text(max_length=50)),
            "prices": spaces.Sequence(spaces.Box(0.0, 10000.0, shape=())),
        })

    def reset(self, seed=None, options=None):
        items = options.get("items") if options and "items" in options else [
            {"id": f"skin_{i}", "wear": np.random.rand(), "collection": "random", "price": np.random.uniform(5, 100)}
            for i in range(np.random.randint(15, 50))  # Random N for example
        ]
        self.items = items
        N = len(self.items)

        # Dynamically set action space
        self.action_space = spaces.MultiDiscrete([N] * self.picks)

        obs = {
            "ids": [item["id"] for item in self.items],
            "wears": [item["wear"] for item in self.items],
            "collections": [item["collection"] for item in self.items],
            "prices": [item["price"] for item in self.items],
        }
        return obs, {}

    def step(self, action):
        assert len(action) == self.picks, "Action must pick exactly 10 items"
        selected = [self.items[i] for i in action]

        # Example reward: low total price + low average wear
        total_price = sum(item["price"] for item in selected)
        avg_wear = sum(item["wear"] for item in selected) / self.picks
        reward = -total_price + (1 - avg_wear) * 10

        return {}, reward, True, False, {}

    def render(self):
        for i, item in enumerate(self.items):
            print(f"[{i}] {item['id']} | Wear: {item['wear']:.2f} | ${item['price']:.2f}")
