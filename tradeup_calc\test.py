from library import simulateTradeup
import orjson
import json

with open("C:\\Users\\<USER>\\Desktop\\code stuff\\tradeup_ai\\get_db\\today.json", "rb") as f:
    data = orjson.loads(f.read())

inputSkins = [{"name": "MP5-SD | Co-Processor", "collection": "The 2018 Nuke Collection", "rarity": "Mil-Spec", "float": 0.11}] * 6
for i in range(4):
    inputSkins.append({"name": "Sawed-Off | First Class", "collection": "The Baggage Collection", "rarity": "Mil-Spec", "float": 0.06})

print(json.dumps(simulateTradeup(inputSkins, data), indent=4))
