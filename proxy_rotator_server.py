import asyncio
import re
import time
import logging
from typing import List, Optional, Dict, Any
from datetime import datetime

import aiohttp
from bs4 import BeautifulSoup
from fastapi import FastAPI, HTTPException, BackgroundTasks
from pydantic import BaseModel

# --------------------
# Logging setup
# --------------------
logging.basicConfig(
    level=logging.DEBUG,  # Change to logging.INFO for less verbosity
    format="%(asctime)s [%(levelname)s] %(message)s",
    datefmt="%Y-%m-%d %H:%M:%S",
)
logger = logging.getLogger(__name__)

app = FastAPI(title="Proxy Rotator / Scraper")

PROXY_SOURCES = [
    # Your existing sources...
    "https://proxyscrape.com/free-proxy-list",
    "https://free-proxy-list.net/",
    "https://spys.one/en/",
    "https://www.freeproxy.world/",
    "https://raw.githubusercontent.com/proxifly/free-proxy-list/refs/heads/main/proxies/all/data.json",

    "https://www.us-proxy.org/",
    "https://www.sslproxies.org/",
    "https://www.socks-proxy.net/",
    "https://proxylist.geonode.com/api/proxy-list?limit=50&page=1&sort_by=lastChecked&sort_type=desc",
    "https://raw.githubusercontent.com/TheSpeedX/PROXY-List/master/http.txt",
    "https://raw.githubusercontent.com/TheSpeedX/PROXY-List/master/socks4.txt",
    "https://raw.githubusercontent.com/TheSpeedX/PROXY-List/master/socks5.txt",
    "https://raw.githubusercontent.com/fate0/proxylist/master/proxy.list",
    "https://raw.githubusercontent.com/clarketm/proxy-list/master/proxy-list-raw.txt",
    "https://raw.githubusercontent.com/monosans/proxy-list/main/proxies/http.txt",
    "https://raw.githubusercontent.com/monosans/proxy-list/main/proxies/socks4.txt",
    "https://raw.githubusercontent.com/monosans/proxy-list/main/proxies/socks5.txt",
    "https://www.proxy-list.download/api/v1/get?type=http",
    "https://www.proxy-list.download/api/v1/get?type=https",
    "https://www.proxy-list.download/api/v1/get?type=socks4",
    "https://www.proxy-list.download/api/v1/get?type=socks5",
    "https://api.proxyscrape.com/?request=getproxies&proxytype=http&timeout=10000&country=all",
    "https://api.proxyscrape.com/?request=getproxies&proxytype=https&timeout=10000&country=all",
    "https://api.proxyscrape.com/?request=getproxies&proxytype=socks4&timeout=10000&country=all",
    "https://api.proxyscrape.com/?request=getproxies&proxytype=socks5&timeout=10000&country=all",

    # Additional sources:
    "https://www.proxy-list.download/api/v1/get?type=http&anon=elite",  # Elite HTTP proxies
    "https://www.proxy-list.download/api/v1/get?type=https&anon=elite",  # Elite HTTPS proxies
    "https://raw.githubusercontent.com/ShiftyTR/Proxy-List/master/proxy.txt",
    "https://raw.githubusercontent.com/jetkai/proxy-list/main/online-proxies/txt/proxies.txt",
    "https://raw.githubusercontent.com/roosterkid/openproxylist/main/HTTPS_RAW.txt",
    "https://raw.githubusercontent.com/roosterkid/openproxylist/main/SOCKS4_RAW.txt",
    "https://raw.githubusercontent.com/roosterkid/openproxylist/main/SOCKS5_RAW.txt",
    "https://raw.githubusercontent.com/hookzof/socks5_list/master/proxy.txt",
    "https://www.freeproxylists.net/",
    "https://proxyspace.pro/",
    "https://www.socks-proxy.net/",
    "https://www.proxy-list.download/api/v1/get?type=socks4&anon=elite",
    "https://www.proxy-list.download/api/v1/get?type=socks5&anon=elite",
    "https://multiproxy.org/txt_all/proxy.txt",
    "https://raw.githubusercontent.com/jetkai/proxy-list/main/online-proxies/txt/socks5.txt",
    "https://www.proxydb.net/?protocol=http",
    "https://www.proxydb.net/?protocol=https",
    "https://www.proxydb.net/?protocol=socks4",
    "https://www.proxydb.net/?protocol=socks5",
    "https://webanetlabs.net/publ/24",

    # manual
    "https://raw.githubusercontent.com/vakhov/fresh-proxy-list/refs/heads/master/proxylist.txt",
    "https://raw.githubusercontent.com/ErcinDedeoglu/proxies/refs/heads/main/proxies/http.txt",
    "https://raw.githubusercontent.com/ErcinDedeoglu/proxies/refs/heads/main/proxies/https.txt",
    "https://raw.githubusercontent.com/ErcinDedeoglu/proxies/refs/heads/main/proxies/socks4.txt",
    "https://raw.githubusercontent.com/ErcinDedeoglu/proxies/refs/heads/main/proxies/socks5.txt",
    "https://github.com/zloi-user/hideip.me/raw/refs/heads/master/http.txt",
    "https://github.com/zloi-user/hideip.me/raw/refs/heads/master/https.txt",
    "https://github.com/zloi-user/hideip.me/raw/refs/heads/master/socks4.txt",
    "https://github.com/zloi-user/hideip.me/raw/refs/heads/master/socks5.txt"

]



IP_PORT_RE = re.compile(r"\b((?:\d{1,3}\.){3}\d{1,3}):(\d{1,5})\b")

proxy_pool: List[Dict[str, Any]] = []
proxy_index = 0
proxy_lock = asyncio.Lock()
targets: Dict[str, Dict[str, Any]] = {}

class Target(BaseModel):
    name: str
    url: str
    method: str = "GET"
    params: Optional[Dict[str, str]] = None
    headers: Optional[Dict[str, str]] = None
    data: Optional[Any] = None
    timeout: float = 15.0

class ScrapeResult(BaseModel):
    added: int
    total_pool: int

def extract_ip_ports_from_text(text: str) -> List[str]:
    found = set()
    for m in IP_PORT_RE.finditer(text):
        ip, port = m.group(1), m.group(2)
        parts = ip.split(".")
        if all(0 <= int(p) <= 255 for p in parts) and 0 < int(port) <= 65535:
            found.add(f"{ip}:{port}")
    return list(found)

async def fetch_text(session: aiohttp.ClientSession, url: str, timeout: int = 15) -> Optional[str]:
    logger.debug(f"Fetching proxy source: {url}")
    try:
        async with session.get(url, timeout=timeout, ssl=False) as resp:
            text = await resp.text()
            logger.debug(f"Fetched {len(text)} chars from {url}")
            return text
    except Exception as e:
        logger.warning(f"Failed to fetch {url}: {e}")
        return None

async def validate_proxy(session: aiohttp.ClientSession, proxy: str, test_url: str = "http://httpbin.org/ip", timeout: float = 8.0) -> Dict[str, Any]:
    schemes = ["http://", "https://", "socks4://", "socks5://"]
    # logger.debug(f"Validating proxy: {proxy}")
    for scheme in schemes:
        proxy_url = proxy if proxy.startswith(("http://", "https://", "socks4://", "socks5://")) else scheme + proxy
        start = time.time()
        try:
            async with session.get(test_url, proxy=proxy_url, timeout=timeout, ssl=False) as r:
                _ = await r.text()
                latency = time.time() - start
                return {"ok": True, "latency": latency, "checked_at": datetime.utcnow().isoformat(), "type": scheme[:-3]}  # remove '://'
        except Exception:
            continue
    return {"ok": False, "latency": None, "checked_at": datetime.utcnow().isoformat(), "type": None}


async def add_proxies_from_source(session: aiohttp.ClientSession, source_url: str) -> List[str]:
    text = await fetch_text(session, source_url)
    if not text:
        return []
    results = set(extract_ip_ports_from_text(text))
    logger.debug(f"Found {len(results)} proxies in raw text from {source_url}")
    try:
        soup = BeautifulSoup(text, "html.parser")
        for table in soup.find_all("table"):
            for row in table.find_all("tr"):
                cols = [c.get_text(strip=True) for c in row.find_all(["td", "th"])]
                if len(cols) >= 2:
                    ip, port = cols[0], cols[1]
                    if re.match(r"^\d{1,3}(\.\d{1,3}){3}$", ip) and re.match(r"^\d{1,5}$", port):
                        results.add(f"{ip}:{port}")
    except Exception as e:
        logger.debug(f"HTML parse error for {source_url}: {e}")
    for line in text.splitlines():
        if re.match(r"^\d{1,3}(\.\d{1,3}){3}:\d{1,5}$", line.strip()):
            results.add(line.strip())
    logger.info(f"Total proxies extracted from {source_url}: {len(results)}")
    return list(results)

async def scrape_proxies(sources: List[str] = None, validate: bool = True, concurrency: int = 10) -> ScrapeResult:
    sources = sources or PROXY_SOURCES
    new_found = set()
    async with aiohttp.ClientSession() as session:
        sem = asyncio.Semaphore(concurrency)
        async def _fetch(source):
            async with sem:
                return await add_proxies_from_source(session, source)
        results = await asyncio.gather(*[asyncio.create_task(_fetch(s)) for s in sources])
        for lst in results:
            new_found.update(lst)
        added = 0
        async with proxy_lock:
            existing = {entry["proxy"] for entry in proxy_pool}
            for ipport in new_found:
                if ipport not in existing:
                    proxy_pool.append({"proxy": ipport, "type": "http", "last_checked": None, "ok": None, "latency": None})
                    added += 1
        logger.info(f"Added {added} new proxies. Total now: {len(proxy_pool)}")
        if validate and added > 0:
            to_check = [p for p in proxy_pool if p["ok"] is None]
            logger.debug(f"Validating {len(to_check)} proxies...")
            async with aiohttp.ClientSession() as s2:
                async def _check(entry):
                    res = await validate_proxy(s2, entry["proxy"])
                    entry.update({"ok": res["ok"], "latency": res["latency"], "last_checked": res["checked_at"]})
                await asyncio.gather(*[asyncio.create_task(_check(e)) for e in to_check])
        async with proxy_lock:
            proxy_pool.sort(key=lambda p: p['latency'] if p['latency'] is not None else float('inf'))
            logger.info("Sorted proxy pool by latency (fastest first)")
    return ScrapeResult(added=added, total_pool=len(proxy_pool))

async def get_next_proxy() -> Optional[str]:
    global proxy_index
    async with proxy_lock:
        if not proxy_pool:
            logger.warning("No proxies available for rotation")
            return None
        # Filter working proxies, sorted by latency ascending
        ok_list = sorted([p for p in proxy_pool if p.get("ok")], key=lambda p: p['latency'] if p['latency'] is not None else float('inf'))
        candidates = ok_list if ok_list else proxy_pool
        proxy_index = (proxy_index + 1) % len(candidates)
        proxy = candidates[proxy_index]["proxy"]
        logger.debug(f"Using proxy: {proxy}")
        return proxy

async def force_rotate(amount: int = 1) -> Optional[str]:
    global proxy_index
    async with proxy_lock:
        if not proxy_pool:
            logger.warning("No proxies available to rotate")
            return None
        proxy_index = (proxy_index + amount) % len(proxy_pool)
        logger.debug(f"Rotated to proxy: {proxy_pool[proxy_index]['proxy']}")
        return proxy_pool[proxy_index]["proxy"]

@app.post("/scrape", response_model=ScrapeResult)
async def api_scrape(validate: bool = True):
    logger.info("Scrape request received")
    return await scrape_proxies(PROXY_SOURCES, validate=validate)

@app.get("/proxies")
async def api_list_proxies(limit: int = 200, valid: Optional[bool] = None):
    logger.info(f"Listing up to {limit} proxies (valid={valid})")
    async with proxy_lock:
        proxies = proxy_pool
        if valid is True:
            proxies = [p for p in proxies if p.get("ok") is True]
        elif valid is False:
            proxies = [p for p in proxies if p.get("ok") is False]

        # Sort by latency ascending, unknown latency goes last
        proxies = sorted(proxies, key=lambda p: p['latency'] if p['latency'] is not None else float('inf'))

        return {
            "count": len(proxies),
            "proxies": proxies[:limit]
        }

@app.post("/targets")
async def api_add_target(t: Target):
    if t.name in targets:
        raise HTTPException(status_code=400, detail="Target name already exists")
    targets[t.name] = t.dict()
    logger.info(f"Added target: {t.name}")
    return {"ok": True, "target": targets[t.name]}

@app.post("/rotate")
async def api_rotate(amount: int = 1):
    logger.info(f"Manual rotate by {amount}")
    p = await force_rotate(amount)
    if not p:
        raise HTTPException(status_code=404, detail="No proxies available")
    return {"current_proxy": p}

@app.get("/run_target")
async def run_target(url: str, method: str = "GET", timeout: float = 15.0, max_tries: int = 10):
    async with proxy_lock:
        proxies = sorted(
            [p for p in proxy_pool if p.get("ok")],
            key=lambda p: p['latency'] if p['latency'] is not None else float('inf')
        )
    if not proxies:
        raise HTTPException(status_code=503, detail="No working proxies available")

    async with aiohttp.ClientSession() as session:
        for attempt, proxy_entry in enumerate(proxies[:max_tries], start=1):
            proxy = proxy_entry["proxy"]
            if not proxy.startswith(("http://", "https://", "socks4://", "socks5://")):
                proxy_url = "http://" + proxy  # default to HTTP
            else:
                proxy_url = proxy

            start_time = time.time()
            try:
                async with session.request(
                    method=method,
                    url=url,
                    timeout=timeout,
                    proxy=proxy_url,
                    ssl=False
                ) as resp:
                    text = await resp.text()
                    latency = time.time() - start_time
                    return {
                        "ok": True,
                        "proxy_used": proxy_url,
                        "status": resp.status,
                        "latency": latency,
                        "response_snippet": text[:500],
                        "attempts": attempt
                    }
            except Exception as e:
                logger.warning(f"Proxy failed ({proxy_url}): {e}")
                continue

    raise HTTPException(status_code=502, detail=f"All {max_tries} proxies failed")
