import logging
import re

from .items_manager import ItemsManager
from .structs import *

class ItemsParser:
    def __init__(self) -> None:
        super().__init__()
        self.items_manager = ItemsManager()
        self.logger = logging.getLogger("ItemsParser")
        self.manual_changes()

        self.souvenir_collections = {
            "set_vertigo":          "The Vertigo Collection",
            "set_inferno":          "The Inferno Collection",
            "set_nuke":             "The Nuke Collection",
            "set_dust_2":           "The Dust 2 Collection",
            "set_train":            "The Train Collection",
            "set_mirage":           "The Mirage Collection",
            "set_italy":            "The Italy Collection",
            "set_lake":             "The Lake Collection",
            "set_safehouse":        "The Safehouse Collection",
            "set_overpass":         "The Overpass Collection",
            "set_cobblestone":      "The Cobblestone Collection",
            "set_cache":            "The Cache Collection",
            "set_nuke_2":           "The 2018 Nuke Collection",
            "set_inferno_2":        "The 2018 Inferno Collection",
            "set_blacksite":        "The Blacksite Collection",
            "set_dust_2_2021":      "The 2021 Dust 2 Collection",
            "set_mirage_2021":      "The 2021 Mirage Collection",
            "set_op10_ancient":     "The Ancient Collection",
            "set_vertigo_2021":     "The 2021 Vertigo Collection"
        }
        self.blank_collections = {
            "set_dust":                 "The Dust Collection",
            "set_aztec":                "The Aztec Collection",
            "set_militia":              "The Militia Collection",
            "set_office":               "The Office Collection",
            "set_assault":              "The Assault Collection",
            "set_bravo_ii":             "The Alpha Collection",
            "set_bank":                 "The Bank Collection",
            "set_baggage":              "The Baggage Collection",
            "set_gods_and_monsters":    "The Gods and Monsters Collection",
            "set_chopshop":             "The Chop Shop Collection",
            "set_kimono":               "The Rising Sun Collection",
            "set_stmarc":               "The St. Marc Collection",
            "set_canals":               "The Canals Collection",
            "set_norse":                "The Norse Collection",
            "set_op10_ct":              "The Control Collection",
            "set_op10_t":               "The Havoc Collection",
            "set_train_2021":           "The 2021 Train Collection"
        }
        self.nonexisting_containers = {
            "crate_sticker_pack_broken_fang":           "Broken Fang Sticker Collection",
            "crate_patch_pack02":                       "Metal Skill Group Patch Collection",
            "crate_patch_pack03":                       "Operation Riptide Patch Collection",
            "crate_sticker_pack_op_riptide_capsule":    "Operation Riptide Sticker Collection",
            "crate_sticker_pack_recoil":                "Recoil Sticker Collection",
            "crate_sticker_pack_riptide_surfshop":      "Riptide Surf Shop Sticker Collection",
            "crate_sticker_pack_shattered_web":         "Shattered Web Sticker Collection",
        }
        self.tinted_graffities_limited = {
            "spray_std_heart": ["Bazooka Pink", "Blood Red", "Brick Red", "Desert Amber", "Dust Brown", "Monster Purple", "Princess Pink", "Tiger Orange", "Tracer Yellow", "War Pig Pink"],
            "spray_std2_lightbulb": ["Shark White", "Tracer Yellow"],
            "spray_std_piggles": ["Bazooka Pink", "Blood Red", "Brick Red", "Desert Amber", "Dust Brown", "Monster Purple", "Princess Pink", "Tiger Orange", "Tracer Yellow", "War Pig Pink"],
            "spray_std_hl_smiley": ["Battle Green", "Bazooka Pink", "Blood Red", "Brick Red", "Cash Green", "Desert Amber", "Dust Brown", "Frog Green", "Jungle Green", "Princess Pink", "Tiger Orange", "Tracer Yellow", "War Pig Pink"],
            "spray_std_moly": ["Bazooka Pink", "Blood Red", "Brick Red", "Desert Amber", "Dust Brown", "Monster Purple", "Princess Pink", "Tiger Orange", "Tracer Yellow", "War Pig Pink"],
            "spray_std_necklace_dollar": ["Battle Green", "Blood Red", "Cash Green", "Desert Amber", "Dust Brown", "Frog Green", "Jungle Green", "Monarch Blue", "Monster Purple", "Shark White", "Tiger Orange", "Tracer Yellow", "War Pig Pink", "Wire Blue"],
            "spray_std_salty": ["Shark White"],
            "spray_std_emo_happy": ["Battle Green", "Bazooka Pink", "Blood Red", "Brick Red", "Cash Green", "Desert Amber", "Dust Brown", "Frog Green", "Jungle Green", "Princess Pink", "Tiger Orange", "Tracer Yellow", "War Pig Pink"],
            "spray_std_dollar": ["Battle Green", "Cash Green", "Frog Green", "Jungle Green"]
        }

    # manual changes to items data done before parsing
    def manual_changes(self):
        pass

    def update_files(self):
        self.items_manager.update_files()
        self.manual_changes()

    # -------------------------------------- PUBLIC HELPERS -------------------------------------- #

    def get_lootlist_by_item_codename(self, codename: str) -> Lootlist | None:
        for lootlist in self.items_manager.lootlists:
            if codename in lootlist.items:
                return lootlist

        return None

    def get_collection_by_item_codename(self, codename: str) -> Collection | None:
        for collection in self.items_manager.collections:
            collection_items = map(lambda x: x.lower(), collection.items)
            if codename in collection_items:
                return collection

    # -------------------------------------- GETTERS -------------------------------------- #

    def get_skins(self):
        def has_stattrak(item: Item, paintkit: Paintkit) -> bool:
            prefab_tree = self.items_manager.get_prefab_tree(item.prefab)
            if "statted_item_base" not in prefab_tree:
                return False

            for collection in self.items_manager.collections:
                if f"[{paintkit.codename}]{item.codename}" in collection.items:
                    if collection.codename in self.blank_collections:
                        return False

            return True

        def has_souvenir(item: Item, paintkit: Paintkit, lootlist: Lootlist | None) -> bool:
            if lootlist is not None:
                for lootlist in self.items_manager.lootlists:
                    if f"[{paintkit.codename}]{item.codename}" in lootlist.items:
                        for container in lootlist.containers:
                            container_prefab_tree = self.items_manager.get_prefab_tree(container.prefab)
                            if "weapon_case_souvenirpkg" in container_prefab_tree:
                                return True

            for collection in self.items_manager.collections:
                if f"[{paintkit.codename}]{item.codename}" in collection.items:
                    if collection.codename in self.souvenir_collections:
                        return True

            if item.codename == "weapon_revolver" and paintkit.codename == "sp_tape":
                return True

            return False

        def has_star_prefix(item: Item) -> bool:
            prefab_tree = self.items_manager.get_prefab_tree(item.prefab)
            return "hands_paintable" in prefab_tree or "melee_unusual" in prefab_tree

        def get_wear_variants(item: Item, paintkit: Paintkit) -> list[str]:
            wear_ranges = {
                "Factory New": [0.00, 0.07],
                "Minimal Wear": [0.07, 0.15],
                "Field-Tested": [0.15, 0.38],
                "Well-Worn": [0.38, 0.45],
                "Battle-Scarred": [0.45, 1.00]
            }

            item_name = self.items_manager.get_market_hash_name(item.name_tag)
            paintkit_name = self.items_manager.get_market_hash_name(paintkit.name_tag)
            if paintkit.codename == "default":
                return [item_name]

            wear_remap_min = self.items_manager.get_paintkit("default").wear_remap_min
            wear_remap_max = self.items_manager.get_paintkit("default").wear_remap_max
            if paintkit.wear_remap_min is not None:
                wear_remap_min = paintkit.wear_remap_min
            if paintkit.wear_remap_max is not None:
                wear_remap_max = paintkit.wear_remap_max

            results = []
            for wear_name, (low, high) in wear_ranges.items():
                if float(wear_remap_min) < high and float(wear_remap_max) > low:
                    results.append((f"{item_name} | {paintkit_name} ({wear_name})", wear_name))

            return results

        def is_weapon(item: Item) -> bool:
            prefab_tree = self.items_manager.get_prefab_tree(item.prefab)
            return "primary" in prefab_tree or "secondary" in prefab_tree

        results = []
        for item in self.items_manager.items:
            if not is_weapon(item):
                continue

            for paintkit in self.items_manager.paintkits:
                if paintkit.codename == "default":
                    continue  # skip default weapons

                skin_codename = f"{item.codename}_{paintkit.codename}"
                if not self.items_manager.skin_exists(skin_codename):
                    continue

                lootlist = self.get_lootlist_by_item_codename(f"[{paintkit.codename}]{item.codename}")
                collection = self.get_collection_by_item_codename(f"[{paintkit.codename}]{item.codename}")

                base_name = self.items_manager.get_market_hash_name(item.name_tag)
                paintkit_name = self.items_manager.get_market_hash_name(paintkit.name_tag)

                # Full skin display name
                skin_name = f"{base_name} | {paintkit_name}"

                # Prefixes
                if has_souvenir(item, paintkit, lootlist):
                    skin_name = f"Souvenir {skin_name}"
                elif has_stattrak(item, paintkit):
                    # skin_name = f"StatTrak™ {skin_name}"
                    pass
                
                if has_star_prefix(item):
                    skin_name = f"★ {skin_name}"

                # Wear range
                wear_min = paintkit.wear_remap_min or self.items_manager.get_paintkit("default").wear_remap_min
                wear_max = paintkit.wear_remap_max or self.items_manager.get_paintkit("default").wear_remap_max

                if "souvenir" not in skin_name.lower():
                    results.append({
                        "skinName": skin_name,
                        # "skinRarity": str(paintkit.rarity),
                        "skinCollection": self.items_manager.get_market_hash_name(collection.name_tag) if collection else None,
                        "wearRange": [float(wear_min), float(wear_max)]
                    })

        return results