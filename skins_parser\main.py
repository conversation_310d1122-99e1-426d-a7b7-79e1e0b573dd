import ujson
import logging

from .items_parser import ItemsParser  # Adjust if this is a module (e.g., use `.items_parser` if inside a package)


def save_to_file(data, output, pretty=False):
    with open(output, "w") as f:
        if pretty:
            f.write(ujson.dumps(data, indent=4))
        else:
            f.write(ujson.dumps(data))

    logging.info(f"Data saved to {output}")


def main():
    logging.basicConfig(level=logging.INFO)

    items_parser = ItemsParser()
    items_parser.update_files()

    logging.info("Parsing weapon skins...")
    skins = items_parser.get_skins()

    save_to_file({"skins": skins}, "out.json", pretty=True)


if __name__ == "__main__":
    main()
