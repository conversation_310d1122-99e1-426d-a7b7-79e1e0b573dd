import json
import time
import os
import orjson

with open("db2.json", "rb") as f:
    data = orjson.loads(f.read())

dates = {}

processed = 0
for collection in data:
    processed += 1 
    print(f"Parsing \x1b[34m{collection}\x1b[37m | {processed}/{len(data)}")
    for rarity in data[collection]:

        if rarity == "Unknown":
            continue

        print(f"---{rarity}")
        previousPrices = {}
        for skin in data[collection][rarity]:
            if "Souvenir" in skin:
                continue
            for date in data[collection][rarity][skin]["prices"]:

                if date not in dates:
                    dates[date] = {}
                if collection not in dates[date]:
                    dates[date][collection] = {}
                if rarity not in dates[date][collection]:
                    dates[date][collection][rarity] = {}
                if skin not in dates[date][collection][rarity]:
                    dates[date][collection][rarity][skin] = {}
                    dates[date][collection][rarity][skin]["prices"] = data[collection][rarity][skin]["prices"][date]
                    dates[date][collection][rarity][skin]["wears"] = data[collection][rarity][skin]["wears"]
                    dates[date][collection][rarity][skin]["inspectLink"] = data[collection][rarity][skin]["inspectLink"]
                    dates[date][collection][rarity][skin]["wearRange"] = data[collection][rarity][skin]["wearRange"]


                for wear in data[collection][rarity][skin]["wears"]:
                    if wear not in data[collection][rarity][skin]["prices"][date]:
                        try:
                            dates[date][collection][rarity][skin]["prices"][wear] = previousPrices[wear]
                        except:
                            pass
                
                previousPrices = dates[date][collection][rarity][skin]["prices"]



processed = 0
for date, dateObj in dates.items():
    processed += 1
    year = date.split(" ")[2]
    
    if not os.path.exists(f"dates/{year}"):
        os.makedirs(f"dates/{year}")
        
    print(f"Writing {date} | {processed}/{len(dates)}")
    with open(f"dates/{year}/{date}.json", "wb") as f:
        f.write(orjson.dumps(dateObj, option=orjson.OPT_INDENT_2))