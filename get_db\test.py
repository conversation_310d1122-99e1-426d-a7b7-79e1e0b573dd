import orjson

with open("db2.json", "rb") as f:
    data = orjson.loads(f.read())

x = 0
c = []

for collection in data:
    for rarity in data[collection]:
        y = 0

        for skin in data[collection][rarity]:
            if "StatTrak™" in skin:
                continue
            if "Souvenir" in skin:
                continue
            y += 1

        if y > x:
            x = y
            c = ["collection: " + collection, "rarity: " + rarity]

print(x, c)