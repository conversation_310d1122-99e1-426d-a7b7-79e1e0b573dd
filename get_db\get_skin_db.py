import json
import requests
import time
import re
import os
import threading
from datetime import datetime, timedelta
from concurrent.futures import ThreadPoolExecutor, as_completed

today_date_str = datetime.now().strftime("%b %d %Y")
steamapisBaseUrl = "https://api.steamapis.com/"
steamapisAPIKEY = "wJ92R1kHGah5Mb2Q1jX5FZeKpuE"

saveEvery = 100
out = {}
lock = threading.Lock()

def get_all_items():
    return requests.get(steamapisBaseUrl + f"market/items/730?api_key={steamapisAPIKEY}",).json()['data']

def parse_items(items):
    data = {"skins": {}}
    for item in items:
        itemName = "NoName"
        wear = ""
        name = item["market_hash_name"].lower()

        if all(x not in name for x in ["knife", "gloves", "daggers", "bayonet", "karambit", "wraps"]):
            for w in ["Factory New", "Minimal Wear", "Field-Tested", "Well-Worn", "Battle-Scarred"]:
                if f"({w})" in item['market_hash_name']:
                    itemName = item['market_hash_name'].replace(f" ({w})", "")
                    wear = w
                    break

        if itemName != "NoName":
            if itemName not in data["skins"]:
                data["skins"][itemName] = {"wears": []}
            data["skins"][itemName]["wears"].append(wear)
    return data["skins"]

def process_skin(skinName, skin):
    global out
    local_output = {}

    for wear in skin["wears"]:
        skinHashName = f"{skinName} ({wear})".replace("\u2122", "™")
        url = f'{steamapisBaseUrl}market/item/730/{skinHashName}?api_key={steamapisAPIKEY}&median_history_days=2000'

        while True:
            res = requests.get(url)
            resJson = res.json()

            if res.status_code != 200:
                if resJson.get("error") == "No matching item found with these parameters":
                    return None
                else:
                    time.sleep(31)
                    continue
            break

        skinCollection, skinRarity, inspectLink = "Unknown", "Unknown", ""
        try:
            tags = resJson.get("assetInfo", {}).get("tags", [])
            if len(tags) > 2:
                skinCollection = tags[2].get("name", "Unknown")
            if len(tags) > 4:
                skinRarity = tags[4].get("name", "Unknown").replace(" Grade", "")
        except:
            pass

        try:
            if "assets" in resJson:
                descriptions = resJson["assets"].get("descriptions", [])
                for d in descriptions:
                    if d.get("name") == "itemset_name":
                        skinCollection = d.get("value", "Unknown")
                        break
                for action in resJson["assets"].get("actions", []):
                    if action.get("name") == "Inspect in Game...":
                        inspectLink = action["link"]
                        break
        except:
            pass

        with lock:
            if skinCollection not in out:
                out[skinCollection] = {}
            if skinRarity not in out[skinCollection]:
                out[skinCollection][skinRarity] = {}
            if skinName not in out[skinCollection][skinRarity]:
                out[skinCollection][skinRarity][skinName] = {
                    "wears": skin["wears"],
                    "inspectLink": inspectLink,
                    "prices": {}
                }

            for datePrice in resJson.get("median_avg_prices_2000days", []):
                if datePrice[0] != today_date_str:
                    if datePrice[0] not in out[skinCollection][skinRarity][skinName]["prices"]:
                        out[skinCollection][skinRarity][skinName]["prices"][datePrice[0]] = {}
                    out[skinCollection][skinRarity][skinName]["prices"][datePrice[0]][wear] = round(datePrice[1], 2)

        with lock:
            print(f"[✓] {skinHashName} → {skinCollection} / {skinRarity}")

    return skinName

def main():
    startTime = time.time()
    print("Loading items from SteamApis...")
    items = get_all_items()
    skins = parse_items(items)
    print(f"Found {len(skins)} skins.")

    with ThreadPoolExecutor(max_workers=8) as executor:
        futures = []
        for skinName, skin in skins.items():
            futures.append(executor.submit(process_skin, skinName, skin))

        processedCount = 0
        for i, future in enumerate(as_completed(futures), 1):
            result = future.result()
            processedCount += 1

            if processedCount % saveEvery == 0:
                with lock:
                    print(f"Saving progress at {processedCount} skins...")
                    with open("out.json", "w") as f:
                        json.dump(out, f, indent=4)

            with lock:
                elapsed = str(timedelta(seconds=int(time.time() - startTime)))
                print(f"Processed: {processedCount}/{len(skins)} | Elapsed: {elapsed}")

    with open("out_final.json", "w") as f:
        json.dump(out, f, indent=4)
    print("✅ Finished all skins.")

if __name__ == "__main__":
    main()
