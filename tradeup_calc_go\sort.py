import orjson

# Load the JSON array from file
with open("result.json", "rb") as f:
    data = orjson.loads(f.read())  # data is a list of dicts

# Sort by Metrics.profitabilityFees (ascending)
sorted_data = sorted(data, key=lambda x: x["Metrics"]["profitabilityFees"])

# Write sorted data back to a file with indentation
with open("result_sorted.json", "wb") as f:
    f.write(orjson.dumps(sorted_data, option=orjson.OPT_INDENT_2))
