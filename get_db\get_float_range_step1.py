from fake_useragent import UserAgent
import requests
import json

with open("csgoskinsgg.json", "r") as f:
    data = json.load(f)

ua = UserAgent()

stattracks = []
out = {}

try:
    with open("out_float_range.json", "r") as f:
        out = json.load(f)
except:
    pass

saveEvery = 10

i = 0
for collection in data:
    for rarity in data[collection]:
        for skin in data[collection][rarity]:
            
            if "souvenir" in skin.lower():
                print("\033[93mSkipping souvenir skin: " + skin)
                continue

            if skin in out:
                print(f"Skin \033[93m {skin} \033[96m already processed. Skipping...")
                continue

            querySkinName = skin.split(" | ")
            querySkinName = querySkinName[0].replace(" ", "-").replace("StatTrak™-", "") + "/" + querySkinName[1].replace(" ", "%20").replace("StatTrak™-", "")

            if skin.replace("StatTrak™ ", "") in out: # if a skin already exists, add a new key for the stattrak version
                stattracks.append(skin)
                print(f"Skin \033[93m {skin} \033[96m has stattrak version. Skipping...")
                continue

            lines = requests.get(f"https://wiki.cs.trade/weapons/{querySkinName}", headers={"User-Agent": ua.random}).text
            line = lines.split("<script id=\"__NEXT_DATA__\" type=\"application/json\">")[1].split(",\"chartData\":")[0] + "}}}"
            try:
                skinData = json.loads(line)["props"]["pageProps"]["skinData"]
                out[skin] = [float(skinData["min_wear"]), float(skinData["max_wear"])]
            except:
                print(line)
                print(f"https://wiki.cs.trade/weapons/{querySkinName}")
                skinData = {"min_wear": "zero", "max_wear": "one"}
                out[skin] = [skinData["min_wear"], skinData["max_wear"]]
                # raise Exception("Failed to load skin data")


            print("\033[94m" + collection, "\033[92m" + rarity, '\033[96m' + querySkinName, out[skin])

            i += 1

            if i % saveEvery == 0:
                print(f"\033[92mSaving after {i} iters...")
                with open("out_float_range_s1.json", "w") as f:
                    json.dump(out, f, indent=4)

print("\033[92mAdding stattrak versions...")
for stattrak in stattracks:
    out[stattrak] = out[stattrak.replace("StatTrak™ ", "")]

print("\033[92mSaving...")
with open("out_float_range_s1.json", "w") as f:
    json.dump(out, f, indent=4)

# lines = requests.get("https://wiki.cs.trade/weapons/Desert-Eagle/Printstream", headers={"User-Agent": ua.random}).text
# line = lines.split("<script id=\"__NEXT_DATA__\" type=\"application/json\">")[1].split(",\"chartData\":")[0] + "}}}"
# skinData = json.loads(line)["props"]["pageProps"]["skinData"]

# print(float(skinData["min_wear"]), (skinData["max_wear"]))