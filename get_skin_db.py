import json
import requests
import time
import re
import os

with open("out.json", "r") as f:
    data = json.load(f)

skins = data["skins"]
wearTiers = {
    "Factory New": [0.00, 0.06999],
    "Minimal Wear": [0.07, 0.14999],
    "Field-Tested": [0.15, 0.37999],
    "Well-Worn": [0.38, 0.44999],
    "Battle-Scarred": [0.45, 1.00]
}

steamapisBaseUrl = "https://api.steamapis.com/market/item/730/"
steamapisAPIKEY = "wJ92R1kHGah5Mb2Q1jX5FZeKpuE"

out = {}

startOffset = 0  # ← change this to skip N entries
saveEvery = 10   # ← save every 10 skins

processedCount = 0  # Counter for saving every N
totalProcessed = 0  # Tracks overall progress (resets after offset)

loadInOldOutput = True
skipOldOutputProcessedSkins = True

processedSkins = {}
if loadInOldOutput:
    if os.path.exists("out_new.json"):
        with open("out_new.json", "r") as f:
            out = json.load(f)
            print("Loaded previous progress from out_new.json")
    else:
        print("No existing progress found. Starting fresh.")

    processedSkins = {
        skinName
        for collection in out.values()
        for rarityGroup in collection.values()
        for skinName in rarityGroup.keys()
    }

    print("processed skins")

print("Starting...")

lastItemName = ""
lastItemPrice = ""
for skin in skins[startOffset:]:
    totalProcessed += 1

    if skipOldOutputProcessedSkins:
        if skin["skinName"] in processedSkins:
            print(f"Skipping {skin['skinName']} as it was already processed.")
            continue

    if "skip" in skin and skin["skip"] == True:
        print(f"Skipping {skin['skinName']} as it was marked to skip.")
        continue

    skin_min, skin_max = skin["wearRange"]

    for wear, tier_range in wearTiers.items():
        tier_min, tier_max = tier_range

        if "skip" in skin and skin["skip"] == True:
            continue

        if skin_max >= tier_min and skin_min <= tier_max:

            if "skip" in skin and skin["skip"] == True:
                continue

            skinHashName = f"{skin['skinName']} ({wear})".replace("\u2122", "™")
            skinName = skin["skinName"]

            print(f"Retrieving for: {skinHashName}")

            # Rate limit retry loop
            while True:
                url = f'{steamapisBaseUrl}{skin["skinName"]} ({wear})?api_key={steamapisAPIKEY}'
                res = requests.get(url)
                resJson = res.json()

                if res.status_code != 200:
                    print(json.dumps(resJson, indent=4))

                    if resJson.get("error") == "No matching item found with these parameters":
                        print("skin doesn't exist")
                        print(res.status_code)
                        print("")
                        break
                    else:
                        print("Rate limited. Waiting 30 seconds...")
                        for _ in range(6):
                            print(".", end="")
                            time.sleep(5)
                        continue

                break

            foundCollection = False
            try:
                if "assetInfo" in resJson:
                    if "tags" in resJson['assetInfo']:
                        if len(resJson["assetInfo"]["tags"]) > 2:
                            if 'name' in resJson["assetInfo"]["tags"][2]:
                                skinCollection = resJson["assetInfo"]["tags"][2]["name"]
                                foundCollection = True
                if foundCollection == False:
                    if "assets" in resJson:
                        if "descriptions" in resJson["assets"]:
                            for description in resJson["assets"]["descriptions"]:
                                if description["name"] == "itemset_name":
                                    foundCollection = True
                                    skinCollection = description["value"]
                                    break
            except:
                skinCollection = "Unknown"
            if foundCollection == False:
                skinCollection = "Unknown"

            foundRarity = False
            try:
                if "assetInfo" in resJson:
                    if "tags" in resJson["assetInfo"]:
                        if len(resJson["assetInfo"]["tags"]) > 4:
                            if "name" in resJson["assetInfo"]["tags"][4]:
                                skinRarity = resJson["assetInfo"]["tags"][4]["name"]
                                foundRarity = True
                if foundRarity == False:
                    if "assets" in resJson:
                        if "type" in resJson["assets"]:
                            skinRarity = resJson["assets"]["type"].split(" ")[0]
                            foundRarity = True
            except:
                skinRarity = "Unknown"
            if foundRarity == False:
                skinRarity = "Unknown"

            skinRarity = skinRarity.replace(" Grade", "")

            try:
                skinPrice = round(resJson["median_avg_prices_15days"][14][1] + 0.01, 2)
            except:
                print("Price not found")
                skinPrice = 999_999_999

            print(f"Retrieved info for: {skinHashName, skinRarity, skinCollection, skinPrice}, Status code: {res.status_code}")
            print("")

            if skinCollection not in out:
                out[skinCollection] = {}
            if skinRarity not in out[skinCollection]:
                out[skinCollection][skinRarity] = {}
            if skinName not in out[skinCollection][skinRarity]:
                out[skinCollection][skinRarity][skinName] = {}
            if "prices" not in out[skinCollection][skinRarity][skinName]:
                out[skinCollection][skinRarity][skinName]["prices"] = {}

            out[skinCollection][skinRarity][skinName].update({'wearRange': skin["wearRange"]})
            out[skinCollection][skinRarity][skinName]["prices"].update({wear: skinPrice})

            if res.status_code == 400 or skinPrice == 999_999_999:
                skin["skip"] = True

            time.sleep(0.05)

    processedCount += 1

    if processedCount % saveEvery == 0:
        print(f"Saving progress after {processedCount} processed skins...")
        with open("out_new2.json", "w") as f:
            json.dump(out, f, indent=4)