import requests
import json
import orjson

steamapisBaseUrl = "https://api.steamapis.com/"
steamapisAPIKEY = "wJ92R1kHGah5Mb2Q1jX5FZeKpuE"

excludeUnstable = True

with open("db_csgoskinsgg.json", "rb") as f:
    data = orjson.loads(f.read())

def get_all_items():
    return requests.get(steamapisBaseUrl + f"market/items/730?api_key={steamapisAPIKEY}",).json()["data"]
# def get_all_items():
#     with open("C:\\Users\\<USER>\\Desktop\\code stuff\\tradeup_ai\\market_items.json", "rb") as f:
#         return orjson.loads(f.read())["data"]

allItems = get_all_items()

items = {}
for item in allItems:

    if excludeUnstable == True and item["prices"]["unstable"] == True:
        continue
    
    # if item["prices"]["median"] == None:
    #     continue

    try:
        price = item["prices"]["latest"]
        if abs(price - item["prices"]["mean"]) > 5:
            price = item["prices"]["mean"]
    except:
        price = item["prices"]["safe_ts"]["last_24h"]
        if price == 0:
            price = item["prices"]["safe_ts"]["last_7d"]

    # try:
    #     price = item["prices"]["safe_ts"]["last_24h"]
    #     if price == 0:
    #         price = item["prices"]["safe_ts"]["last_7d"]
    #     if price == 0:
    #         raise Exception("Price is 0")
    # except:
    #     if (item["prices"]["latest"] > item["prices"]["median"] * 2):
    #         price = item["prices"]["latest"]
    #     else:
    #         price = item["prices"]["median"]

    image = item["image"]
    item = item["market_name"]

    if " (" not in item:
        continue

    skip = False
    for subs in ["★", "knife", "gloves", "daggers", "bayonet", "karambit", "wraps", "sticker"]:
        if subs in item.lower():
            skip = True
    if skip:
        continue
    
    try:
        name, wear = item.split(" (")
    except:
        name, wear = item.split(") (")
        name = name + ")"

    wear = wear[:-1]

    if name not in items:
        items[name] = {"prices": {}}
    if wear not in items[name]["prices"]:
        items[name]["prices"][wear] = price
    items[name]["image"] = image
    
for collection in data:
    for rarity in data[collection]:
        print(rarity)
        for skin in data[collection][rarity]:
            if skin in items:
                data[collection][rarity][skin]["image"] = items[skin]["image"]
                for wear, price in items[skin]["prices"].items():

                    print(skin, wear, price)

                    data[collection][rarity][skin]["prices"][wear] = price
                    
                    # if wear not in data[collection][rarity][skin]["wears"]:
                    #     data[collection][rarity][skin]["wears"].append(wear)

# toDel = []
# for collection in data:
#     for rarity in data[collection]:
#         for skin in data[collection][rarity]:
#             for price in data[collection][rarity][skin]["prices"]:
#                 if data[collection][rarity][skin]["prices"][price] == 0:
#                     toDel.append([collection, rarity, skin, price])

# for item in toDel:
#     del data[item[0]][item[1]][item[2]]["prices"][item[3]]

for collection in data:
    for rarity in data[collection]:
        for skin in data[collection][rarity]:
            if wear not in data[collection][rarity][skin]["wears"]:
                data[collection][rarity][skin]["wears"].append(wear)


with open("today.json", "w") as f:
    f.write(json.dumps(data, indent=4))


# print(json.dumps(allItems, indent=4))