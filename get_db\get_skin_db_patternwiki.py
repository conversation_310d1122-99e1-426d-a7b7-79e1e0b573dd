import requests
import json
from bs4 import BeautifulSoup

html_doc = requests.get('https://pattern.wiki/collections/').text

soup = BeautifulSoup(html_doc, 'html.parser')

container_div = soup.find("div", {"class": "container-flex"})

collections = {}

csWeapons = ["CZ75-Auto", "Desert Eagle", "Dual Berettas", "Five-SeveN", "Glock-18", "P2000", "P250", "R8 Revolver", "Tec-9", "USP-S",
             "AK-47", "AUG", "FAMAS", "Galil AR", "M4A1-S", "M4A4", "SG 553", "AWP", "G3SG1", "SCAR-20", "SSG 08",
             "MAC-10", "MP5-SD", "MP7", "MP9", "P90", "PP-Bizon", "UMP-45",
             "MAG-7", "Sawed-Off", "Nova", "XM1014", "M249", "Negev"
             ]
floats = {
    "Factory New": [0,  0.07],
    "Minimal Wear": [0.6999, 0.15],
    "Field-Tested": [0.14999, 0.37],
    "Well-Worn": [0.36999, 0.44],
    "Battle-Scarred": [0.43999, 1.00]
}
def getWearsFromFloatRange(floatRange):
    wears = []
    for wear, wearRange in floats.items():
        if floatRange[0] <= wearRange[0] and floatRange[1] >= wearRange[1]:
            wears.append(wear)
    return wears

out = {}

for child in container_div.find_all(recursive=False):
    collectionName = child.find("h5").text
    collectionUrl = 'https://pattern.wiki' + child.a['href']
    collections[collectionName] = collectionUrl
    out[collectionName] = {}

for collection, collectionsUrl in collections.items():
    html_doc = requests.get(collectionsUrl).text
    soup = BeautifulSoup(html_doc, 'html.parser')

    content_wrapper = soup.find("div", {"id": "content-wrapper"})

    for rarity in content_wrapper.div.find_all("div", {"class": "container"}, recursive=False):

        if rarity.h2.text == "Special":
            continue

        skinRarity = rarity.h2.text.split(" ")[0]

        if rarity not in out[collection]:
            out[collection][skinRarity] = {}

        for skin in rarity.div.find_all("div", recursive=False):
            skinName = skin.a.h5.text

            for weapon in csWeapons:
                if weapon in skinName:
                    skinName = skinName.replace(weapon, f"{weapon} |")

            print(skinName)

            wearRes = requests.get(f"https://pattern.wiki{skin.a['href']}").text
            skinWearRange = BeautifulSoup(wearRes, 'html.parser')
            skinWearRange = skinWearRange.find("div", {"class": "wear-bar"}).find_all("span", recursive=False)
            skinWearRange = [float(skinWearRange[0].text.lstrip().rstrip()), float(skinWearRange[1].text.lstrip().rstrip())]
            skinWearRange[0], skinWearRange[1] = round(skinWearRange[0], 2), round(skinWearRange[1], 2)

            # try: # GET SKIN FLOAT RANGE FROM IT'S OWN PAGE, FLOAT RANGES FROM COLLECTION PAGE SEEM TO BE INACCURATE (eg: desolate space)
            #     # skinWearRange = skin.parent.find_all('span')[0].text.split(" - ")
            #     skinWearRange = [round(float(skinWearRange[0]), 2), round(float(skinWearRange[1]), 2)]
            # except:
            #     # skinWearRange = skin.parent.find_all('span')[1].text.split(" - ")
            #     skinWearRange = [round(float(skinWearRange[0]), 2), round(float(skinWearRange[1]), 2)]

            wears = getWearsFromFloatRange(skinWearRange)
            prices = {}
            for wear in wears:
                prices[wear] = 0.00

            if skinName not in out[collection][skinRarity]:
                out[collection][skinRarity][skinName] = {"wearRange": skinWearRange, "wears": wears, "prices": prices}

            print(skinName, skinRarity, skinWearRange)

with open("patternwiki_db.json", "w") as f:
    json.dump(out, f, indent=4)