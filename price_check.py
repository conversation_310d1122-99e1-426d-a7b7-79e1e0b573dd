import json
import requests
from concurrent.futures import ThreadPoolExecutor, as_completed
import time

# Wear data
wear_ranges = {
    "Factory New": [0.00, 0.07],
    "Minimal Wear": [0.07, 0.15],
    "Field-Tested": [0.15, 0.38],
    "Well-Worn": [0.38, 0.45],
    "Battle-Scarred": [0.45, 1.00]
}

wear_keys = {
    "Factory New": "fn",
    "Minimal Wear": "mw",
    "Field-Tested": "ft",
    "Well-Worn": "ww",
    "Battle-Scarred": "bs"
}

# Load your input data
with open('out.json', 'r') as file:
    data = json.load(file)

def price_check_threaded(market_hash_name, cooldown=50, max_retries=12):
    url = f"https://steamcommunity.com/market/pricehistory/?appid=730&market_hash_name={market_hash_name}"
    headers = {
      'Accept': '*/*',
      'Accept-Language': 'pl-PL,pl;q=0.9,en-US;q=0.8,en;q=0.7',
      'Cache-Control': 'no-cache',
      'Connection': 'keep-alive',
      'Pragma': 'no-cache',
      'Referer': 'https://steamcommunity.com/profiles/76561199815144946/inventory',
      'Sec-Fetch-Dest': 'empty',
      'Sec-Fetch-Mode': 'cors',
      'Sec-Fetch-Site': 'same-origin',
      'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36',
      'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"',
      'sec-ch-ua-mobile': '?0',
      'sec-ch-ua-platform': '"Windows"',
      'Cookie': 'timezoneOffset=7200,0; browserid=3352451844407848451; recentlyVisitedAppHubs=359550%2C2344520%2C1966720%2C730; cookieSettings=%7B%22version%22%3A1%2C%22preference_state%22%3A1%2C%22content_customization%22%3Anull%2C%22valve_analytics%22%3Anull%2C%22third_party_analytics%22%3Anull%2C%22third_party_content%22%3Anull%2C%22utm_enabled%22%3Atrue%7D; sessionid=4b3aa8f37339946b34549f9e; steamCountry=PL%7C3f442171542cf5e06d928ac153a6f3f3; steamLoginSecure=76561199815144946%7C%7CeyAidHlwIjogIkpXVCIsICJhbGciOiAiRWREU0EiIH0.***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.qq1N47o1gkU3_qEhevu5zRMKVkYm-mgba0xqRuOuegaYr5mx1DxGYwQhwtxuhLpGb-7VBt7bcqrlOsQ_EdEzAQ; webTradeEligibility=%7B%22allowed%22%3A0%2C%22reason%22%3A64%2C%22allowed_at_time%22%3A0%2C%22steamguard_required_days%22%3A15%2C%22new_device_cooldown_days%22%3A0%2C%22expiration%22%3A1752620334%2C%22time_checked%22%3A1752620034%7D; sessionid=25efc25d9e33e882c41a79de; steamCountry=PL%7C3f442171542cf5e06d928ac153a6f3f3'
    }

    attempts = 0
    while attempts <= max_retries:
        try:
            response = requests.get(url, headers=headers, timeout=10)
            response = response.json()

            if isinstance(response.get("prices"), list) and len(response["prices"]) > 0 and len(response["prices"][-1]) > 1:
                print(f"{response['prices'][-1][1]} ||| {market_hash_name}")
                return response["prices"][-1][1]
            else:
                print(f"NO DATA ||| {market_hash_name}")
                return 999_999_999

        except Exception as e:
            if str(e) == "'NoneType' object has no attribute 'get'" and attempts < max_retries:
                print(f"RATE LIMIT HIT ||| {market_hash_name} ||| Retry {attempts + 1}/{max_retries} after {cooldown}s")
                time.sleep(cooldown)
                attempts += 1
                continue
            else:
                print(f"NO DATA EXCEPTION ||| {market_hash_name}")
                print(e)
                return 999_999_999

# Prepare all jobs first
price_jobs = []

for item in data["skins"]:
    item["prices"] = {}
    wear_min = item.get("wearMin", 0.0)
    wear_max = item.get("wearMax", 1.0)

    for wear_name, (range_min, range_max) in wear_ranges.items():
        if wear_max >= range_min and wear_min <= range_max:
            key = wear_keys.get(wear_name)
            if key:
                job_key = (item, key, f"{item['skinName']} ({wear_name})")
                price_jobs.append(job_key)

# Run threads to fetch prices
with ThreadPoolExecutor(max_workers=10) as executor:
    future_to_job = {
        executor.submit(price_check_threaded, market_hash_name): (item, key)
        for item, key, market_hash_name in price_jobs
    }

    for future in as_completed(future_to_job):
        item, key = future_to_job[future]
        item["prices"][key] = future.result()

# Organize into collections
collections = {}

for item in data["skins"]:
    collection_name = item.get("skinCollection", "Unknown Collection")
    rarity_name = item.get("skinRarity", "Unknown Rarity")

    match rarity_name:
        case "common":
            item["rarityCommonName"] = "Consumer/white"
        case "uncommon":
            item["rarityCommonName"] = "Industrial/light-blue"
        case "rare":
            item["rarityCommonName"] = "Mil-Spec/blue"
        case "mythical":
            item["rarityCommonName"] = "Restricted/purple"
        case "legendary":
            item["rarityCommonName"] = "Classified/pink"
        case "ancient":
            item["rarityCommonName"] = "Covert/red"
        case "immortal":
            item["rarityCommonName"] = "Contraband/orange"

    # if collection_name not in collections:
    #     collections[collection_name] = {}
    # if rarity_name not in collections[collection_name]:
    #     collections[collection_name][rarity_name] = []
    # collections[collection_name][rarity_name].append(item)

final_output = {
    "collections": collections
}

with open('out2.json', 'w') as file:
    json.dump(final_output, file, indent=4)
