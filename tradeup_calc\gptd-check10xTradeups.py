from library import simulateTradeup
import orjson
import json
from concurrent.futures import ProcessPoolExecutor, as_completed
from itertools import product
from functools import partial

with open("C:\\Users\\<USER>\\Desktop\\code stuff\\tradeup_ai\\get_db\\today.json", "rb") as f:
    data = orjson.loads(f.read())

floats = [0.06, 0.11, 0.26]
combinationsToCheck = [[8, 2], [7, 3], [6, 4], [5, 5], [10, 0]]
onlyProfitable = True
maxInWorth = 15

valid_skins = [
    {"name": skin, "collection": collection, "rarity": rarity}
    for collection, rarities in data.items()
    for rarity, skins in rarities.items()
    for skin in skins
    if "Souvenir" not in skin
]

def simulate_pair(skin1, skin2, flo1, flo2, combination):
    results = []
    collection1, rarity1, name1 = skin1["collection"], skin1["rarity"], skin1["name"]
    collection2, rarity2, name2 = skin2["collection"], skin2["rarity"], skin2["name"]

    if name1 == name2 or rarity1 != rarity2:
        return results

    if "StatTrak™" in name1 and "StatTrak™" not in name2:
        return results
    if "StatTrak™" not in name1 and "StatTrak™" in name2:
        return results

    try:
        if sorted(combination) == [0, 10]:
            inputSkins = [{"name": name1, "collection": collection1, "rarity": rarity1, "float": flo1}] * 10
        else:
            inputSkins = [{"name": name1, "collection": collection1, "rarity": rarity1, "float": flo1}] * combination[0]
            inputSkins += [{"name": name2, "collection": collection2, "rarity": rarity2, "float": flo2}] * combination[1]

        res = simulateTradeup(inputSkins, data)

        if onlyProfitable and res["profitabilityFees"] < 105:
            return results
        if res["inputWorth"] > maxInWorth:
            return results

        if sorted(combination) == [0, 10]:
            key = (
                f"10x {name1} | flo: {flo1} ~ {rarity1} ~|~ "
                f"{res['oddsToProfitFee']}% ~|~ "
                f"{res['inputWorth']} ~|~ {res['outputWorth']}"
            )
        else:
            key = (
                f"{combination[0]}x {name1} | flo: {flo1} ~ {rarity1} ~|~ "
                f"{combination[1]}x {name2} | flo: {flo2} ~ {rarity2} ~|~ "
                f"{res['oddsToProfitFee']}% ~|~ "
                f"{res['inputWorth']} ~|~ {res['outputWorth']}"
            )

        results.append((key, res["profitabilityFees"]))
    except:
        pass

    return results

tasks = []

for skin1 in valid_skins:
    collection1, rarity1, name1 = skin1["collection"], skin1["rarity"], skin1["name"]
    skin1FloatRange = data[collection1][rarity1][name1]["wearRange"]

    print(skin1, collection1)
    for flo1 in floats:
        if not (skin1FloatRange[0] <= flo1 <= skin1FloatRange[1]):
            continue
        for skin2 in valid_skins:
            collection2, rarity2, name2 = skin2["collection"], skin2["rarity"], skin2["name"]
            skin2FloatRange = data[collection2][rarity2][name2]["wearRange"]

            for flo2 in floats:
                if not (skin2FloatRange[0] <= flo2 <= skin2FloatRange[1]):
                    continue
                for combination in combinationsToCheck:
                    tasks.append((skin1, skin2, flo1, flo2, combination))

outcomes = {}

with ProcessPoolExecutor(max_workers=8) as executor:
    futures = [executor.submit(simulate_pair, *task) for task in tasks]
    for future in as_completed(futures):
        for key, value in future.result():
            outcomes[key] = value

# Sort outcomes by profitability
outcomes = dict(sorted(outcomes.items(), key=lambda item: item[1]))

# Save results
with open("G:\\res2.json", "w") as f:
    json.dump(outcomes, f, indent=4)
