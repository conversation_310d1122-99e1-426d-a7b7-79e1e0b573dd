import json
import orj<PERSON>

with open("db.json", "rb") as f:
    data = orjson.loads(f.read())

fullyUnknown = []
rarityUnknown = []

for collection in data:
    for rarity in data[collection]:
        for skin in data[collection][rarity]:
            if rarity == "Unknown" and collection != "Unknown":
                
                print("Found unknown rarity")
                rarityUnknown.append([skin, collection])
                data[collection][rarity][skin]["toBeDeleted"] = True

            if rarity == "Unknown" and collection == "Unknown":
                print("Found unknown collection and rarity")
                fullyUnknown.append(skin)
                data[collection][rarity][skin]["toBeDeleted"] = True


# print(data["Unknown"])
# crash

for skin in fullyUnknown:
    skinsCollection = input(f"\x1b[31mcollection \x1b[33mfor \x1b[32m{skin}: ")
    skinsRarity = input(f"\x1b[35mrarity \x1b[33mfor \x1b[32m{skin}: ")

    if skinsCollection not in data:
        data[skinsCollection] = {}
    if skinsRarity not in data[skinsCollection]:
        data[skinsCollection][skinsRarity] = {}

    data[skinsCollection][skinsRarity][skin] = data["Unknown"]["Unknown"][skin]
    
    del data["Unknown"]["Unknown"][skin]

skinRaritiesForStattrackObj = {}
lastRarity = ""
for skin in rarityUnknown:
    if "StatTrak™" not in skin[0]:
        print("""
        1. Consumer
        2. Industrial
        3. Mil-Spec
        4. Restricted
        5. Classified
        6. Covert
        7. Contraband
        """)
        print(skin[1])
        skinsRarity = input(f"\x1b[35mrarity \x1b[33mfor \x1b[32m{skin}: ")
        match skinsRarity:
            case 1:
                skinsRarity = "Consumer"
            case 2:
                skinsRarity = "Industrial"
            case 3:
                skinsRarity = "Mil-Spec"
            case 4:
                skinsRarity = "Restricted"
            case 5:
                skinsRarity = "Classified"
            case 6:
                skinsRarity = "Covert"
            case 7:
                skinsRarity = "Contraband"

        if skinsRarity == "":
            skinsRarity = lastRarity

        lastRarity = skinsRarity

        if skinsRarity not in data[skin[1]]:
            data[skin[1]][skinsRarity] = {}

        data[skin[1]][skinsRarity][skin[0]] = data[skin[1]]["Unknown"][skin[0]]
        skinRaritiesForStattrackObj[skin[0]] = skinsRarity
        
        del data[skin[1]]["Unknown"][skin[0]]

for skin in rarityUnknown:
    if "StatTrak™" in skin[0]:

        skinsRarity = skinRaritiesForStattrackObj[skin[0].replace("StatTrak™ ", "")]

        if skinsRarity not in data[skin[1]]:
            data[skin[1]][skinsRarity] = {}

        data[skin[1]][skinsRarity][skin[0]] = data[skin[1]]["Unknown"][skin[0]]

        del data[skin[1]]["Unknown"][skin[0]]


del data["Unknown"]

toDelete = []
for collection in data:
    for rarity in data[collection]:
        if rarity == "Unknown":
            toDelete.append(data[collection]["Unknown"])

for item in toDelete:
    del item

with open("db2.json", "w") as f:
    json.dump(data, f, indent=4)