from library import simulateTradeup
import orjson
import json

with open("C:\\Users\\<USER>\\Desktop\\code stuff\\tradeup_ai\\get_db\\today.json", "rb") as f:
    data = orjson.loads(f.read())

# with open("C:\\Users\\<USER>\\Desktop\\code stuff\\tradeup_ai\\get_db\\dates\\2025\\Jun 17 2025.json", "rb") as f:
    # data = orjson.loads(f.read())

floats = [0.06, 0.14, 0.62]

outcomes = {}

# for collection in data:
#     print(f"===== \033[94m{collection}\033[0m =====")
#     for rarity in data[collection]:
#         print(f"\033[93m--- {rarity} ---\033[0m")
#         for skin in data[collection][rarity]:
#             if "Souvenir" in skin:
#                 continue

#             print(skin)

#             for flo in floats:
#                 inputSkins = [{"name": skin, "collection": collection, "rarity": rarity, "float": flo}] * 10
#                 if collection in ["The Ascent Collection", "The Fever Collection", "The Train 2025 Collection", "The Radiant Collection", "The Boreal Collection", "The Sport & Field Collection", "The Graphic Design Collection", "The Overpass 2024 Collection", "The Gallery Collection"]:
#                     continue
#                 try:
#                     res = simulateTradeup(inputSkins, data)
#                     outcomes[skin + " ~|~ " + str(flo) + " ~ " + rarity + " ~ " + collection + " ~|~ " + str(res["inputWorth"]) + " ~|~ " + str(res["outputWorth"])] = res["profitabilityFees"]
#                 except:
#                     outcomes[skin + " " + str(flo)] = -3

valid_skins = [
    {"name": skin, "collection": collection, "rarity": rarity}
    for collection, rarities in data.items()
    for rarity, skins in rarities.items()
    for skin in skins
    if "Souvenir" not in skin
]
for skin1 in valid_skins:
    collection1, rarity1, name1 = skin1["collection"], skin1["rarity"], skin1["name"]

    print(skin1)
    skin1FloatRange = data[collection1][rarity1][name1]["wearRange"]

    for flo1 in floats:
        if not (skin1FloatRange[0] <= flo1 <= skin1FloatRange[1]):
            continue
        for skin2 in valid_skins:
            collection2 = skin2["collection"]

            rarity2, name2 = skin2["rarity"], skin2["name"]
            if name1 == name2:
                continue

            skin2FloatRange = data[collection2][rarity2][name2]["wearRange"]

            if rarity2 != rarity1:
                continue

            if "StatTrak™" in name1 and "StatTrak™" not in name2:
                continue
            if "StatTrak™" not in name1 and "StatTrak™" in name2:
                continue


            for flo2 in floats:
                if not (skin2FloatRange[0] <= flo2 <= skin2FloatRange[1]):
                    continue
                inputSkins = [{"name": name1, "collection": collection1, "rarity": rarity1, "float": flo1}] * 8
                inputSkins.append({"name": name2, "collection": collection2, "rarity": rarity2, "float": flo2})
                inputSkins.append({"name": name2, "collection": collection2, "rarity": rarity2, "float": flo2})

                try:
                    res = simulateTradeup(inputSkins, data)
                    # if res["profitabilityFees"] > 105 and res["profitabilityFees"] < 205:
                    key = (
                        f"8x {name1} | flo: {flo1} ~ {rarity1} ~|~ "
                        f"2x {name2} | flo: {flo2} ~ {rarity2} ~|~ "
                        f"{res['oddsToProfitFee']}% ~|~ "
                        f"{res['inputWorth']} ~|~ {res['outputWorth']}"
                    )

                    if res["inputWorth"] > 10:
                        continue

                    outcomes[key] = res["profitabilityFees"]
                except:
                    pass
# 55555 XXXXX
# for skin1 in valid_skins:
#     collection1, rarity1, name1 = skin1["collection"], skin1["rarity"], skin1["name"]

#     print(skin1)
#     skin1FloatRange = data[collection1][rarity1][name1]["wearRange"]

#     for flo1 in floats:
#         if not (skin1FloatRange[0] <= flo1 <= skin1FloatRange[1]):
#             continue
#         for skin2 in valid_skins:
#             collection2 = skin2["collection"]

#             rarity2, name2 = skin2["rarity"], skin2["name"]
#             if name1 == name2:
#                 continue

#             skin2FloatRange = data[collection2][rarity2][name2]["wearRange"]

#             if rarity2 != rarity1:
#                 continue

#             if "StatTrak™" in name1 and "StatTrak™" not in name2:
#                 continue
#             if "StatTrak™" not in name1 and "StatTrak™" in name2:
#                 continue


#             for flo2 in floats:
#                 if not (skin2FloatRange[0] <= flo2 <= skin2FloatRange[1]):
#                     continue
#                 inputSkins = [{"name": name1, "collection": collection1, "rarity": rarity1, "float": flo1}] * 5
#                 inputSkins.append({"name": name2, "collection": collection2, "rarity": rarity2, "float": flo1})
#                 inputSkins.append({"name": name2, "collection": collection2, "rarity": rarity2, "float": flo2})
#                 inputSkins.append({"name": name2, "collection": collection2, "rarity": rarity2, "float": flo2})
#                 inputSkins.append({"name": name2, "collection": collection2, "rarity": rarity2, "float": flo2})
#                 inputSkins.append({"name": name2, "collection": collection2, "rarity": rarity2, "float": flo2})

#                 try:
#                     res = simulateTradeup(inputSkins, data)
#                     if res["profitabilityFees"] > 105 and res["profitabilityFees"] < 205:
#                         key = (
#                             f"5x {name1} | flo: {flo1} ~ {rarity1} ~|~ "
#                             f"5x {name2} | flo: {flo2} ~ {rarity2} ~|~ "
#                             f"{res['oddsToProfitFee']}% ~|~ "
#                             f"{res['inputWorth']} ~|~ {res['outputWorth']}"
#                         )

#                         if res["inputWorth"] < 10:
#                             continue

#                         outcomes[key] = res["profitabilityFees"]
#                 except:
#                     pass

outcomes = dict(sorted(outcomes.items(), key=lambda item: item[1]))

with open("G:\\res2.json", "w") as f:
    json.dump(outcomes, f, indent=4)