package main

import (
	"encoding/json"
	"fmt"
	"math"
	"net/http"
	_ "net/http/pprof"
	"os"
	"strings"
	"sync"
	"time"
	tu "tradeup_calc/tradeup"
	"tradeup_calc/types"
)

var pow10 = [...]float64{
    1, 10, 100, 1000, 10000, 100000, 1000000,
}

func roundFloat(val float64, precision uint) float64 {
    if precision < uint(len(pow10)) {
        ratio := pow10[precision]
        return math.Round(val*ratio) / ratio
    }
    ratio := math.Pow(10, float64(precision))
    return math.Round(val*ratio) / ratio
}


func main() {

	go func() {
    	fmt.Println("pprof listening on :6060")
    	http.ListenAndServe("localhost:6060", nil)
	}()

	floats := []float64{0.06, 0.11, 0.26, 0.41, 0.60}
	combinationsToCheck := [][2]int{{9, 1}, {8, 2}, {7, 3}, {6, 4}, {5, 5}, {10, 0}}
    profitThreshold := 120.0

	args := os.Args[1:]
	specifiedSkins := false
	if len(args) > 0 {
		specifiedSkins = true
	}


	start := time.Now()

	// Read the JSON file
	data, err := os.ReadFile(`today.json`)
	if err != nil {
		fmt.Println("Error reading file:", err)
		return
	}

	// Unmarshal into AllCollections type
	var all types.AllCollections
	if err := json.Unmarshal(data, &all); err != nil {
		panic(err)
	}

	// Precompute valid floats per skin
	validFloatsPerSkin := make(map[string][]float64)
	var validFloats []float64
	for collectionName, collection := range all {
		for rarityName, rarity := range collection {
			for skinName, skinData := range rarity {
				for _, f := range floats {
					if f >= skinData.WearRange[0] && f <= skinData.WearRange[1] {
						validFloats = append(validFloats, f)
					}
				}
				key := collectionName + "|" + rarityName + "|" + skinName
				validFloatsPerSkin[key] = validFloats
			}
		}
	}

	var outcomes []struct {
		InputItems []struct {
			Name       string
			Wear       float64
			Quantity   int
			Rarity     string
			Price      float64
			Collection string
		}
		OutputItems []struct {
			Name       string
			Wear       float64
			Quantity   int
			Rarity     string
			Price      float64
			Collection string
            Chance   float64
		}
		Metrics types.OutputMetrics
		Stattrak bool
	}

	var mu sync.Mutex
	var wg sync.WaitGroup

	for collectionName, collection := range all {
		wg.Add(1)

		
		go func(collectionName string, collection types.Collection) {
			defer wg.Done()

			tradeupSimLocalCache := types.LocalCache{
				LocalOutputSkinCache: make(map[types.OutputSkinKey]types.OutputSkin),
			}

			var inputSkins [10]types.InputSkin
			tenXChecked := make(map[string]bool)

			for rarityName, rarity := range collection {

				if rarityName == "Covert" {
					continue
				}

				for skinName, skinData := range rarity {

					// check if skinName is in args, if specified skins
					if specifiedSkins {  // we do this here, because we don't need to check the second skin, unless combinations contains [0, 10], which it shouldn't either way
						shouldSkip := true
						for _, skin := range args {
							if skin == skinName {
								shouldSkip = false
								break
							}
						}
						if shouldSkip {
							continue
						}
					}

					key := collectionName + "|" + rarityName + "|" + skinName
                    
					// fmt.Println(skinName)
                    var stattrak bool
                    if strings.HasPrefix(skinName, "Stat") {
                        stattrak = true
                    } else {
                        stattrak = false
                    }

					validFloats := validFloatsPerSkin[key]

					skinWearMin := skinData.WearRange[0]
					skinWearMax := skinData.WearRange[1]

					for _, flo := range validFloats {
						
						// Safety check (shouldn't be needed since pre-filtered)
						if flo < skinWearMin || flo > skinWearMax {
							continue
						}

						// Adjust flo if equals to wear min
						if flo == skinWearMin {
							if skinWearMin == 0.06 {
								flo = 0.065
							} else {
								flo += 0.02
							}
						}

						for collectionName2, collection2 := range all {
							for rarityName2, rarity2 := range collection2 {
								if rarityName2 != rarityName {
									continue
								}

								for skinName2, skinData2 := range rarity2 {
									if skinName == skinName2 {
										continue
									}

                                    if stattrak && !strings.HasPrefix(skinName2, "Stat") {
                                        continue
                                    }
									if !stattrak && strings.HasPrefix(skinName, "Stat") {
                                        continue
                                    }

									key2 := collectionName2 + "|" + rarityName2 + "|" + skinName2
									validFloats2 := validFloatsPerSkin[key2]

									// Cache wear range for skinData2
									skin2WearMin := skinData2.WearRange[0]
									skin2WearMax := skinData2.WearRange[1]

									for _, flo2 := range validFloats2 {
										// Check wear range for flo2 using cached values
										if flo2 < skin2WearMin || flo2 > skin2WearMax {
											continue
										}

										// Adjust flo2 if equals to wear min
                                        if flo2 == skin2WearMin {
                                            if skin2WearMin == 0.06 {
                                                flo2 = 0.065
                                            } else {
                                                flo2 += 0.02
                                            }
                                        }

										for _, combination := range combinationsToCheck {

											// skip redundant calculations
											if combination[0] == 10 {
												if _, ok := tenXChecked[key]; ok {
													continue
												}
												tenXChecked[key] = true
											}


											// check prices, if any is 0 continue
											if skinData.Prices[tu.WearFloatToStr(flo)] == 0 || skinData2.Prices[tu.WearFloatToStr(flo2)] == 0 {
												continue
											}

											// Fill inputSkins according to combination

											for i := 0; i < combination[0]; i++ {
												inputSkins[i] = types.InputSkin{Name: skinName, Wear: flo, Rarity: rarityName, Collection: collectionName}
											}
											for i := combination[0]; i < 10; i++ {
												inputSkins[i] = types.InputSkin{Name: skinName2, Wear: flo2, Rarity: rarityName2, Collection: collectionName2}
											}

											tradeupResult, returnedCache, err := tu.SimulateTradeup(all, tradeupSimLocalCache, inputSkins, stattrak)
											tradeupSimLocalCache = returnedCache
											if err != "" {
												// fmt.Println(err)
												continue
											}

											if tradeupResult.Metrics.ProfitabilityFees > profitThreshold { //! I should fix this up, but... it works and I don't want to deal with this shit load of types
												
                                                inputItems := []struct {
                                                    Name       string
                                                    Wear       float64
                                                    Quantity   int
                                                    Rarity     string
                                                    Price      float64
                                                    Collection string
                                                }{
                                                    {
                                                        Name:       skinName,
                                                        Wear:       flo,
                                                        Quantity:   10, // default to 10, will overwrite if needed
                                                        Rarity:     rarityName,
                                                        Price:      skinData.Prices[tu.WearFloatToStr(flo)],
                                                        Collection: collectionName,
                                                    },
                                                }

                                                // If second skin exists, update first quantity and append the second
                                                if combination[1] != 0 {
                                                    inputItems[0].Quantity = combination[0]
                                                    inputItems = append(inputItems, struct {
                                                        Name       string
                                                        Wear       float64
                                                        Quantity   int
                                                        Rarity     string
                                                        Price      float64
                                                        Collection string
                                                    }{
                                                        Name:       skinName2,
                                                        Wear:       flo2,
                                                        Quantity:   combination[1],
                                                        Rarity:     rarityName2,
                                                        Price:      skinData2.Prices[tu.WearFloatToStr(flo2)],
                                                        Collection: collectionName2,
                                                    })
                                                }

                                                var outputItems []struct {
                                                    Name       string
                                                    Wear       float64
                                                    Quantity   int
                                                    Rarity     string
                                                    Price      float64
                                                    Collection string
                                                    Chance   float64
                                                }
                                                for _, outItem := range tradeupResult.Skins {
                                                    outputItems = append(outputItems, struct {
                                                        Name       string
                                                        Wear       float64
                                                        Quantity   int
                                                        Rarity     string
                                                        Price      float64
                                                        Collection string
                                                        Chance   float64
                                                    }{
                                                        Name:       outItem.Name,
                                                        Wear:       outItem.Wear,
                                                        Quantity:   1,
                                                        Rarity:     outItem.Rarity,
                                                        Price:      outItem.Price,
                                                        Collection: outItem.Collection,
                                                        Chance:   roundFloat(outItem.Chance, 4),
                                                    })
                                                }

                                                outcome := struct {
                                                        InputItems []struct {
                                                        Name       string
                                                        Wear       float64
                                                        Quantity   int
                                                        Rarity     string
                                                        Price      float64
                                                        Collection string
                                                    }
                                                    OutputItems []struct {
                                                        Name       string
                                                        Wear       float64
                                                        Quantity   int
                                                        Rarity     string
                                                        Price      float64
                                                        Collection string
                                                        Chance   float64
                                                    }
                                                    Metrics   types.OutputMetrics
                                                    Stattrak  bool
                                                }{
                                                    InputItems:  inputItems,
                                                    OutputItems: outputItems,
                                                    Metrics:     tradeupResult.Metrics,
                                                    Stattrak:    stattrak,
                                                }

												fmt.Println(outcome)

                                                mu.Lock()

												outcomes = append(outcomes, outcome)

                                                mu.Unlock()
											}
										}
									}
								}
							}
						}
					}
				}
			}
		}(collectionName, collection)
	}

	wg.Wait()

	// deduping

	var uniqueMu sync.Mutex
	unique := make([]struct {
		InputItems  []struct {
			Name       string
			Wear       float64
			Quantity   int
			Rarity     string
			Price      float64
			Collection string
		}
		OutputItems []struct {
			Name       string
			Wear       float64
			Quantity   int
			Rarity     string
			Price      float64
			Collection string
			Chance     float64
		}
		Metrics  types.OutputMetrics
		Stattrak bool
	}, 0, len(outcomes))
	seen := sync.Map{}

	for _, o := range outcomes {
		wg.Add(1)
		o := o // capture for closure
		go func() {
			defer wg.Done()
			var sb strings.Builder
			for _, in := range o.InputItems {
				sb.WriteString(in.Name)
				sb.WriteString("|")
				sb.WriteString(fmt.Sprintf("%.4f", in.Wear))
				sb.WriteString("|")
				sb.WriteString(fmt.Sprintf("%d", in.Quantity))
				sb.WriteString("|")
			}
			key := sb.String()
			if _, loaded := seen.LoadOrStore(key, true); !loaded {
				uniqueMu.Lock()
				unique = append(unique, o)
				uniqueMu.Unlock()
			}
		}()
	}

	wg.Wait()
	outcomes = unique

	// Marshal outcomes to pretty JSON
	jsonBytes, err := json.MarshalIndent(outcomes, "", "  ")
	if err != nil {
		panic(err)
	}

	// Write JSON to file
	if err := os.WriteFile("result.json", jsonBytes, 0644); err != nil {
		fmt.Println("Error writing JSON to file:", err)
		return
	}

	fmt.Println("Done! Results written to result.json")
	fmt.Println("Time taken:", time.Since(start))
}