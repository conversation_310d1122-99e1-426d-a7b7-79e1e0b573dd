import orjson

with open(f"C:\\Users\\<USER>\\Desktop\\code stuff\\tradeup_ai\\get_db\\dates\\2025\\Jul 18 2025.json", "rb") as f:
    data = orjson.loads(f.read())

rarityVocab = {
    "Consumer": 1,
    "Industrial": 2,
    "Mil-Spec": 3,
    "Restricted": 4,
    "Classified": 5,
    "Covert": 6,
    "Contraband": 7
}

wearsVocab = {
    "Factory New": 1,
    "Minimal Wear": 2,
    "Field-Tested": 3,
    "Well-Worn": 4,
    "Battle-Scarred": 5
}

collectionVocab = {}
for collection in data:
    if collection not in collectionVocab:
        collectionVocab[collection] = len(collectionVocab)

vocabMap = rarityVocab | wearsVocab | collectionVocab

with open(f"vocab.json", "wb") as f:
    f.write(orjson.dumps(vocabMap))