import orjson
import json
from collections import Counter

year = "2025"
date = "Jul 18"

rarities = ["Consumer", "Industrial", "Mil-Spec", "Restricted", "Classified", "Covert", "Contraband"]
# with open(f"C:\\Users\\<USER>\\Desktop\\code stuff\\tradeup_ai\\get_db\\dates\\{year}\\{date + ' ' + year}.json", "rb") as f:
#     data = orjson.loads(f.read())

with open(f"C:\\Users\\<USER>\\Desktop\\code stuff\\tradeup_ai\\get_db\\today.json", "rb") as f:
    data = orjson.loads(f.read())

def WearFloatToStr(wear: float) -> str:
    if 0 <= wear < 0.07000001:
        return "Factory New"
    elif 0.07000001 <= wear < 0.15000001:
        return "Minimal Wear"
    elif 0.15000001 <= wear < 0.38000001:
        return "Field-Tested"
    elif 0.38000001 <= wear < 0.45000001:
        return "Well-Worn"
    elif 0.45000001 <= wear <= 1000001:
        return "Battle-Scarred"
    else:
        return "Invalid wear value"
    
def calculateSteamNet(price):
    # Valve's cut
    V_raw = round(price / 11.5, 3)
    V = round(V_raw - (V_raw % 0.01), 2)  # round down to 2 digits
    V = max(V, 0.01)

    # Game developer cut
    G_raw = round(price / 23, 3)
    G = round(G_raw - (G_raw % 0.01), 2)  # round down to 2 digits
    G = max(G, 0.01)

    # Net amount received
    net = price - V - G
    return round(net - 0.01, 2)


# inputSkins = [
#     {"name": "MP7 | Motherboard", "collection": "The 2018 Nuke Collection", "rarity": "Industrial", "float": 0.04},
#     {"name": "MP7 | Motherboard", "collection": "The 2018 Nuke Collection", "rarity": "Industrial", "float": 0.04},
#     {"name": "MP7 | Motherboard", "collection": "The 2018 Nuke Collection", "rarity": "Industrial", "float": 0.04},

#     {"name": "Galil AR | Acid Dart", "collection": "The Boreal Collection", "rarity": "Industrial", "float": 0.24},
#     {"name": "Galil AR | Acid Dart", "collection": "The Boreal Collection", "rarity": "Industrial", "float": 0.24},
#     {"name": "Galil AR | Acid Dart", "collection": "The Boreal Collection", "rarity": "Industrial", "float": 0.24},
#     {"name": "Galil AR | Acid Dart", "collection": "The Boreal Collection", "rarity": "Industrial", "float": 0.24},
#     {"name": "Galil AR | Acid Dart", "collection": "The Boreal Collection", "rarity": "Industrial", "float": 0.24},
#     {"name": "Galil AR | Acid Dart", "collection": "The Boreal Collection", "rarity": "Industrial", "float": 0.24},
#     {"name": "Galil AR | Acid Dart", "collection": "The Boreal Collection", "rarity": "Industrial", "float": 0.24},
# ]

inputSkins = [{"name": "StatTrak™ Negev | Terrain", "collection": "The Phoenix Collection", "rarity": "Mil-Spec", "float": 0.11}] * 10

# inputSkins = [{"name": "StatTrak™ MAC-10 | Pipe Down", "collection": "The Danger Zone Collection", "rarity": "Restricted", "float": 0.72}] * 10

# inputSkins = [{"name": "MP5-SD | Co-Processor", "collection": "The 2018 Nuke Collection", "rarity": "Mil-Spec", "float": 0.11}] * 6
# for i in range(4):
#     inputSkins.append({"name": "Sawed-Off | First Class", "collection": "The Baggage Collection", "rarity": "Mil-Spec", "float": 0.06})

# inputSkins = [{"name": "M4A1-S | Boreal Forest", "collection": "The Italy Collection", "rarity": "Industrial", "float": 0.23}] * 10

# inputSkins = [
#     {"name": "StatTrak™ G3SG1 | Scavenger", "collection": "The Danger Zone Collection", "rarity": "Restricted", "float": 0.04},
#     {"name": "StatTrak™ G3SG1 | Scavenger", "collection": "The Danger Zone Collection", "rarity": "Restricted", "float": 0.04},
#     {"name": "StatTrak™ G3SG1 | Scavenger", "collection": "The Danger Zone Collection", "rarity": "Restricted", "float": 0.04},
#     {"name": "StatTrak™ G3SG1 | Scavenger", "collection": "The Danger Zone Collection", "rarity": "Restricted", "float": 0.04},
#     {"name": "StatTrak™ G3SG1 | Scavenger", "collection": "The Danger Zone Collection", "rarity": "Restricted", "float": 0.04},
#     {"name": "StatTrak™ G3SG1 | Scavenger", "collection": "The Danger Zone Collection", "rarity": "Restricted", "float": 0.04},

#     {"name": "StatTrak™ MAG-7 | Petroglyph", "collection": "The Gamma 2 Collection", "rarity": "Restricted", "float": 0.11},
#     {"name": "StatTrak™ MAG-7 | Petroglyph", "collection": "The Gamma 2 Collection", "rarity": "Restricted", "float": 0.11},
#     {"name": "StatTrak™ MAG-7 | Petroglyph", "collection": "The Gamma 2 Collection", "rarity": "Restricted", "float": 0.11},
#     {"name": "StatTrak™ MAG-7 | Petroglyph", "collection": "The Gamma 2 Collection", "rarity": "Restricted", "float": 0.11},
# ]

inputtedStattrak = True if "StatTrak™" in inputSkins[0]["name"] else False
inputtedCollections = []
inputtedFloats = [skin["float"] for skin in inputSkins]
inputtedRarity = inputSkins[0]["rarity"]

outcomeRarity = rarities[rarities.index(inputtedRarity) + 1]
averageFloat = sum(inputtedFloats) / len(inputtedFloats)

for skin in inputSkins:
    inputtedCollections.append(skin["collection"])

inputtedCollections = Counter(inputtedCollections)

print(inputtedCollections)

outcomeSkins = []

for collection in inputtedCollections:
    print(data[collection])
    for rarity in data[collection]:
        if rarity == outcomeRarity:
            for skin in data[collection][rarity]:
                
                # basic check for stattrak
                if inputtedStattrak and "StatTrak™" not in skin:
                    continue
                if not inputtedStattrak and "StatTrak™" in skin:
                    continue


                # https://www.reddit.com/r/GlobalOffensiveTrade/comments/3a57au/psa_float_values_and_tradeups/
                """
                y=range * x + min=(max-min)*x+min
                x=average float value of the 10 skins used
                y=float value of the skins received
                min= minimum of the FV range of the skin received
                range=max-min
                """

                # https://www.tradeupspy.com/tools/trade-up-guide
                " outcome skin float = ((max.float-min.float) * avg.float) + min.float "


                minFloat = data[collection][rarity][skin]["wearRange"][0]
                maxFloat = data[collection][rarity][skin]["wearRange"][1]
                averageFloat = averageFloat # why the fuck not


                outcomeFloat = ( ( maxFloat - minFloat ) * averageFloat ) + minFloat
                outcomeFloat = round(outcomeFloat, 4)
                outcomeSkins.append({"name": skin, "collection": collection, "rarity": rarity, "wearRange": data[collection][rarity][skin]["wearRange"], "float": outcomeFloat, "wear": WearFloatToStr(outcomeFloat)})
                # print(skin, collection, rarity, data[collection][rarity][skin]["wearRange"], outcomeFloat, WearFloatToStr(outcomeFloat))

ballots = 0

outcomeCollections = []
for skin in outcomeSkins:
    outcomeCollections.append(skin["collection"])
outcomeCollections = Counter(outcomeCollections)
# print(outcomeCollections)

inputWorth = 0
for skin in inputSkins:
    ballots += outcomeCollections[skin["collection"]]
    skin["price"] = data[skin["collection"]][skin["rarity"]][skin["name"]]["prices"][WearFloatToStr(skin["float"])]
    print(skin)
    inputWorth += skin["price"]

print("|")

outputWorth = 0
oddsToProfit = 0
oddsToProfitFee = 0
for skin in outcomeSkins:
    skin["chance"] = inputtedCollections[skin["collection"]] / ballots
    skin["price"] = data[skin["collection"]][skin["rarity"]][skin["name"]]["prices"][skin["wear"]]
    print(skin)
    outputWorth += skin["price"]*skin["chance"]

    if skin["price"] > inputWorth:
        oddsToProfit += 1
    if calculateSteamNet(skin["price"]) > inputWorth:
        oddsToProfitFee += 1

oddsToProfit = round((oddsToProfit / len(outcomeSkins)) * 100, 2)
oddsToProfitFee = round((oddsToProfitFee / len(outcomeSkins)) * 100, 2)


# print(json.dumps(outcomeSkins, indent=4))

averageFloat = round(averageFloat, 4)
print(f"averageFloat: {averageFloat}")
inputWorth = round(inputWorth, 2)
print(f"inputWorth: {inputWorth}")
outputWorth = round(outputWorth, 2)
print(f"outputWorth: {outputWorth}")
outcomeWorthFees = round(calculateSteamNet(outputWorth), 2)
print(f"outcomeWorthFees: {outcomeWorthFees}")
profit = round(outputWorth - inputWorth, 2)
print(f"profit: {profit}")
profitFees = round(calculateSteamNet(outputWorth) - inputWorth, 2)
print(f"profitFees: {profitFees}")
profitability = round((outputWorth/inputWorth)*100, 2)
print(f"profitability: {profitability}")
profitabilityFees = round(((calculateSteamNet(outputWorth)/inputWorth)*100), 2)
print(f"profitabilityFees: {profitabilityFees}")

print(f"oddsToProfit: {oddsToProfit}")
print(f"oddsToProfitFee: {oddsToProfitFee}")