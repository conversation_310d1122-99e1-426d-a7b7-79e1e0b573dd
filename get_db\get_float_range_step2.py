from fake_useragent import UserAgent
import requests
import json
from bs4 import BeautifulSoup as bs

ua = UserAgent()

out = {}

try:
    with open("out_float_range_s1.json", "r") as f:
        out = json.load(f)
except:
    pass

skinsToDelete = []
for skin in out:
    if "StatTrak™" in skin:
        skinsToDelete.append(skin)

for skin in skinsToDelete:
    del out[skin]

skins = {}

collectionLinks = ["https://pattern.wiki/collections/set_timed_drops_cool/", "https://pattern.wiki/collections/set_community_35/", "https://pattern.wiki/collections/set_train_2025/", "https://pattern.wiki/collections/set_xpshop_wpn_01/", "https://pattern.wiki/collections/set_timed_drops_warm/", "https://pattern.wiki/collections/set_timed_drops_neutral/", "https://pattern.wiki/collections/set_realism_camo/", "https://pattern.wiki/collections/set_graphic_design/", "https://pattern.wiki/collections/set_overpass_2024/", "https://pattern.wiki/collections/set_community_34/"]

for collectionUrl in collectionLinks:
    res = requests.get(collectionUrl, headers={"User-Agent": ua.random})
    soup = bs(res.text, "html.parser")
    cardBodies = soup.find_all('div', class_='card-body')
    print(len(cardBodies))
    for cardBody in cardBodies:

        wearRes = requests.get(f"https://pattern.wiki{cardBody.parent.parent['href']}").text
        skinWearRange = bs(wearRes, 'html.parser')
        skinWearRange = skinWearRange.find("div", {"class": "wear-bar"}).find_all("span", recursive=False)
        skinWearRange = [float(skinWearRange[0].text.lstrip().rstrip()), float(skinWearRange[1].text.lstrip().rstrip())]
        skinWearRange[0], skinWearRange[1] = round(skinWearRange[0], 2), round(skinWearRange[1], 2)

        print(cardBody.h5.text, skinWearRange)
        # try:
        #     wear = cardBody.parent.find_all('span')[0].text.split(" - ")
        #     wear = [round(float(wear[0]), 2), round(float(wear[1]), 2)]
        # except:
        #     wear = cardBody.parent.find_all('span')[1].text.split(" - ")
        #     wear = [round(float(wear[0]), 2), round(float(wear[1]), 2)]
        skins[cardBody.h5.text] = skinWearRange

# print(skins)

for skin in out:
    if out[skin][0] == "zero" and out[skin][1] == "one":
        try:
            out[skin][0] = skins[skin.replace("| ", "")][0]
            out[skin][1] = skins[skin.replace("| ", "")][1]
        except:
            print(skin)
                    

with open("out_float_range_final.json", "w") as f:
    json.dump(out, f, indent=4)