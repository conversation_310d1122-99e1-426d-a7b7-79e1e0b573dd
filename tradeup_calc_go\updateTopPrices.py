import requests
import orjson
import statistics
import math
import time

def wear_float_to_str(wear: float) -> str:
    if 0 <= wear < 0.07:
        return "Factory New"
    elif 0.07 <= wear < 0.15:
        return "Minimal Wear"
    elif 0.15 <= wear < 0.38:
        return "Field-Tested"
    elif 0.38 <= wear < 0.45:
        return "Well-Worn"
    elif 0.45 <= wear <= 1.0:
        return "Battle-Scarred"
    else:
        return "Invalid wear value"

with open("result.json", "rb") as f:
    results = orjson.loads(f.read())

fineToOverwrite = False
with open("today.json", "rb") as f:
    today = orjson.loads(f.read())

    #? goofy ahh check
    if today["The Revolution Collection"]["Covert"]["M4A4 | Temukau"]["wearRange"][1] == 0.8:
        fineToOverwrite = True

if not fineToOverwrite:
    raise Exception("Failed loading in today.json")

topTradeupProfit = 0
topTradeups = []

for tradeup in results:
    if tradeup["Metrics"]["profitabilityFees"] > topTradeupProfit:
        topTradeupProfit = tradeup["Metrics"]["profitabilityFees"]

for tradeup in results:
    if tradeup["Metrics"]["profitabilityFees"] >= topTradeupProfit-5:
        topTradeups.append(tradeup)

skinsChecked = set()

concurrentSkips = 0

for topTradeup in topTradeups:

    skinsToCheck = []
    for skin in topTradeup["InputItems"]:
        skinsToCheck.append(skin)
    for skin in topTradeup["OutputItems"]:
        skinsToCheck.append(skin)

    for skin in skinsToCheck:
        skinHashName = f"{skin["Name"]} ({wear_float_to_str(skin["Wear"])})"

        if skinHashName in skinsChecked:
            continue
        skinsChecked.add(f"{skin["Name"]} ({wear_float_to_str(skin["Wear"])})")

        print(skinHashName, skin["Price"])

        if concurrentSkips >= 6:
            continue

        listingsUrl = f"https://steamcommunity.com/market/listings/730/{skinHashName}/render/?query=&start=0&count=10&language=english&currency=1&norender=1"
        print(listingsUrl)
        res = requests.get(listingsUrl)
        time.sleep(2)

        

        if res.status_code == 429:
            concurrentSkips += 1
            continue
            # raise Exception("Failed to get listings")
        concurrentSkips = 0

        # print(res.json()["listinginfo"])

        prices = []
        for listingID, listing in res.json()["listinginfo"].items():
            if listing.get("converted_price", 0) == 0:
                continue
            # print(listing)
            prices.append(listing["converted_price"] + listing["converted_fee"])
        print(prices)

        if len(prices) == 0:
            continue #? skip if no prices

        if (1-min(prices)/max(prices))*100 > 5:
            newPrice = max(prices) / 100
        else:
            modes = statistics.multimode(prices)
            medianOfModes = statistics.median(modes)
            
            newPrice = math.ceil(medianOfModes) / 100
        print(newPrice)

        today[skin["Collection"]][skin["Rarity"]][skin["Name"]]["prices"][wear_float_to_str(skin["Wear"])] = newPrice

        print(today[skin["Collection"]][skin["Rarity"]][skin["Name"]])

with open("today.json", "wb") as f:
    f.write(orjson.dumps(today, option=orjson.OPT_INDENT_2))