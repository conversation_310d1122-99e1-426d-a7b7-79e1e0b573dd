import requests
import json
import orjson
from bs4 import BeautifulSoup
import time

with open("today_temp.json", "rb") as f:
    collections = orjson.loads(f.read())
    
x = []
for collection, colelcitonObj in collections.items():
    x.append(collection)
collections = x

headers = {
  'accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
  'accept-language': 'pl-PL,pl;q=0.9,en-US;q=0.8,en;q=0.7',
  'cache-control': 'no-cache',
  'pragma': 'no-cache',
  'priority': 'u=0, i',
  'referer': 'https://csgoskins.gg/collections/the-boreal-collection',
  'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"',
  'sec-ch-ua-mobile': '?0',
  'sec-ch-ua-platform': '"Windows"',
  'sec-fetch-dest': 'document',
  'sec-fetch-mode': 'navigate',
  'sec-fetch-site': 'same-origin',
  'sec-fetch-user': '?1',
  'upgrade-insecure-requests': '1',
  'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36',
  'Cookie': 'GvZ18GVkcDBO=eyJpdiI6IlRUMDNONXFqWHNHVjA5Y1dnOUdLd2c9PSIsInZhbHVlIjoiNFNRNndFWnMxcG9mNGJxVVJGRGdrT2JxMkROdlQyUzl6MCs2bVU4NjNXb2JKL1pWK1hWSmF4V0Flc3loTGt2ZWNJYVJPa1FzQkdxSGV1dWJkZUJaZmJDWUFaUjZRTjVsOWh4amJUZnYvQkpZUGVtNFVnWkFMQk1BdktPMDlSdmxyZEVtQXpFbDBVK01LM0RMOGVUS1BENFFleU9CWGlXTVdPZ3BYaGhXa0tHR21GQmRDUW1nbS9lVjZXVVNOQmNDUG1MWnp4SFpCZmlvbG11VU9OaWtwZy9RMUkxUFF4ZHhaY2hOZ2hqdGdDaFlNSEE0NURVRGh3T21OK0xXU2xqaHlDZ1lVSXVlbzV1OERXSUw1NXg4K1Jqc1VyYXF0K2tRLzV5RFMrcUxuL3hiZXdBMDNYNVNwcFRLSWwyL1hQYk1za0tOdWdBYlpiMVNzb0RlUEwweUQwVjNYYUFYbnltY2ZEclNINWdiczFBPSIsIm1hYyI6ImI5YzY0N2I5MDYxMGNiMTIyMzc0NzRmMjY2MzVkNDJkYTVmN2FlOTkxMTEwYjgxYzI4MzFiYWQyN2Q2NjVlNjgiLCJ0YWciOiIifQ%3D%3D; XSRF-TOKEN=eyJpdiI6IkRNbFJ0eENNZ1NLVnkzc09hK2d4ckE9PSIsInZhbHVlIjoiejVOS2RPVEZmMTlhSEFackluN2NDZ2Vqcm05SWkvUURSVEhQK3FuamJRbnZ4ZmxJSTVQWnhSMmVtU04yYUR0clcySHRwTkt4RzZ2VitxQ1RDR0pZdy9qVlJZdC9tVjVhN3JuYVVqN1JsL2MwWXBSaUxEcGczM2cxTlVrM1hXR0MiLCJtYWMiOiIyYjVhZmI0MTZiOTM2YjY0MDgxN2U0YzFiZmNiNmFlZTNkNTI5NGNkMmFlODlhOTNmNGIwNGM0Yzg2MDAyYmJhIiwidGFnIjoiIn0%3D; csgoskinsgg_session=eyJpdiI6Ik5pV0NCa1B0bnhOdmVISnJRTXU5b1E9PSIsInZhbHVlIjoiMGdKMUh4d2pCYitYZjh1ZXk5QmQ3a1pCdHB2aDF2aFRDVGtEZHE3REt0aCswMGFCdTlkc0NwdHVQMXFtaXVqd3dja0VFMnhJZUNlMG5tdnBBU2RiN1ZHMFhPWWFXRi8rV0NuQjM0VjhKNE4rUlNVRFhyWE1QeE4rSkJzQm56MnYiLCJtYWMiOiJiZGRmN2I5YjJmMTUyNDZhZDMxZTFkOGQ4YWMwM2NjMTdhZWNlMTBlZDM5NjdlMmE2NTk3NjlkMGJmYmQ2ODRmIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IksyVUZ0bHJBMjAzVWtURHltWE9ZMWc9PSIsInZhbHVlIjoiOVowKzdkV3l0ODJJa3luWFZ3RFZKK2RlVVZWbTJPWkhVc3hiZE12b3ljbFpMakhsY0k1aXRJRlRIVncvMlN0LzAxV0NUNVkzdys0UjhtMTJSWFVMT0dMOE11Wnh5dTVKaTlmRmpBT0xPZE1iU0o4K1pReHRLaldlVUdkVUM4TkkiLCJtYWMiOiJmODZmN2I3ZTY5N2Q5MDQ1NzVjMmY5NzdiMGQ5ZTE0YzJhYTgyMDg0YmNmMDVkZDIxNzVhM2E3MDRmMzY5Zjk5IiwidGFnIjoiIn0%3D; csgoskinsgg_session=eyJpdiI6IjY3S0E2S1h2UDA4dVBiYzYzZyt6N0E9PSIsInZhbHVlIjoidFBUZG85NUlVelYvc0FXZUpzdmxrQmxaa0tHbGtuc3hiKy9HeEI3ai83NGRySFd0amgvUjFjT2dDVjNsZFhXWm93UGM3YVo2Rkp3c1A3UjJwVlJrSVk5NDF0Z29WcklJaW5lZDZXcis3MnNpWkJmK2lrWXNWdytBNnpzeE9KMnAiLCJtYWMiOiI3MWNkNjY3NDE5ZjRjODUxMmUxYmY3Njk4NDg4ZTQ2ZDU1OTYyYzYyY2Q0YWQ2NGRmZTUwMzg5NTg3NDc5MzU2IiwidGFnIjoiIn0%3D'
}


out = {}

for collection in collections:
      
	if collection not in out:
		out[collection] = {}

	collectionURI = collection.strip().lower().replace(" ", "-").replace("&-", "").replace(".", "")

	# print(collection)

	succeded = False
	retries = 6
	while succeded == False and retries > 0:
		response = requests.get(f"https://csgoskins.gg/collections/{collectionURI}", headers=headers)

		soup = BeautifulSoup(response.text, "html.parser")

		main = soup.main

		try:
			skinsMainContainer = main.find_all("div", recursive=False)[2]
			succeded = True
		except:
			print(soup.prettify())
			retries -= 1
			time.sleep(10)

	if not succeded:
		out[collection] = "Failed"
		continue

	for skin in skinsMainContainer.find_all("div", recursive=False):
		weaponName, skinName = skin.h2.find_all("span")
		weaponName, skinName = weaponName.text.strip(), skinName.text.strip()
		skinRarity = skin.div.find_all("div", recursive=False)[1].text.strip().split(" ")[0]
		skinStattrak = True if "StatTrak" in skin.div.find_all("div", recursive=False)[2].text.strip() else False

		weaponMarketName = f"{weaponName} | {skinName}"

		# print(skinName, collection)

		if skinRarity not in out[collection]:
			out[collection][skinRarity] = {}

		if weaponMarketName not in out[collection][skinRarity]:
			out[collection][skinRarity][weaponMarketName] = {}

		if skinStattrak and "StatTrak™ " + str(weaponMarketName) not in out[collection][skinRarity]:
			out[collection][skinRarity]["StatTrak™ " + str(weaponMarketName)] = {}

		print(f"{weaponName} | {skinName} ({skinRarity}) /// {collection}")

with open("csgoskinsgg.json", "w") as f:
	json.dump(out, f, indent=4)