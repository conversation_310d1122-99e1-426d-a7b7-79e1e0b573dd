package tradeup

import (
	"math"
	"strings"
	"sync"
	"tradeup_calc/types"
	"fmt"
)

var rarities = [6]string{"Consumer", "Industrial", "Mil-Spec", "Restricted", "Classified", "Covert"}

func nextRarity(current string) (string, bool) {
	for i, r := range rarities {
		if r == current {
			if i+1 < len(rarities) {
				return rarities[i+1], true
			}
			return "", false
		}
	}
	return "", false
}

func WearFloatToStr(wear float64) string {
	switch {
	case wear >= 0 && wear < 0.07:
		return "Factory New"
	case wear >= 0.07 && wear < 0.15:
		return "Minimal Wear"
	case wear >= 0.15 && wear < 0.38:
		return "Field-Tested"
	case wear >= 0.38 && wear < 0.45:
		return "Well-Worn"
	case wear >= 0.45 && wear <= 1.0:
		return "Battle-Scarred"
	default:
		return "Invalid wear value"
	}
}

func calculateSteamNet(price float64) float64 {
	if price <= 0 {
		return 0
	}

	roundTo := func(val, step float64) float64 {
		// Scale to integer multiples of step
		scaled := val / step
		// Round manually by adding 0.5 for positive, -0.5 for negative
		if scaled >= 0 {
			scaled = float64(int(scaled + 0.5))
		} else {
			scaled = float64(int(scaled - 0.5))
		}
		r := scaled * step
		if r < 0.01 {
			return 0.01
		}
		return r
	}

	V := roundTo(price/11.5, 0.01)
	G := roundTo(price/23, 0.01)

	net := price - V - G - 0.01
	if net < 0 {
		net = 0
	}

	// Round final result to 2 decimals
	return math.Round(net*100) / 100
}

const invStep2 = 100.0 // 1 / 0.01

func roundFloat2(val float64) float64 {
    if val >= 0 {
        return float64(int64(val*invStep2 + 0.5)) / invStep2
    }
    return float64(int64(val*invStep2 - 0.5)) / invStep2
}

const invStep4 = 10000.0 // 1 / 0.0001

func roundFloat4(val float64) float64 {
    if val >= 0 {
        return float64(int64(val*invStep4 + 0.5)) / invStep4
    }
    return float64(int64(val*invStep4 - 0.5)) / invStep4
}

// Caches
var (
	inputPriceCache sync.Map
	// inputPriceCache      = make(map[string]float64)
	// inputPriceCacheMutex sync.RWMutex

	outputSkinCache sync.Map
	// outputSkinCache      = make(map[types.OutputSkinKey]types.OutputSkin)
	// outputSkinCacheMutex sync.RWMutex
)

func SimulateTradeup(data types.AllCollections, localCache types.LocalCache, inputSkins [10]types.InputSkin, stattrak bool) (types.TradeupOutput, types.LocalCache, string) {
	if inputSkins[0].Rarity == "Covert" {
		return types.TradeupOutput{}, localCache, "covert skins are not eligible for tradeups"
	}

	tradeupRarity := inputSkins[0].Rarity
	tradeupOutcomeRarity, ok := nextRarity(tradeupRarity)
	if !ok {
		return types.TradeupOutput{}, localCache, "no next rarity found"
	}

	for i := 1; i < len(inputSkins); i++ {
		if inputSkins[i].Rarity != tradeupRarity {
			return types.TradeupOutput{}, localCache, "all skins must be the same rarity"
		}
		isStat := strings.HasPrefix(inputSkins[i].Name, "Stat")
		if isStat != stattrak {
			return types.TradeupOutput{}, localCache, "all skins must be stattrak or not stattrak"
		}
	}

	// --- Calculate input worth using cache ---
	var inputWorth float64
	for _, v := range inputSkins {
		// Try load first

		wearStr := WearFloatToStr(v.Wear)
		inputCacheKey := struct{
			name string
			wear string
		}{
			name: v.Name,
			wear: wearStr,
		}

		// priceI, ok := inputPriceCache.Load(v.Name)
		priceI, ok := inputPriceCache.Load(inputCacheKey)
		var price float64
		if ok {
			price = priceI.(float64)
		} else {
			price = data[v.Collection][v.Rarity][v.Name].Prices[wearStr]
			inputPriceCache.Store(inputCacheKey, price)
			// inputPriceCache.Store(v.Name, price)
		}

		if price == 0 {
			fmt.Println("no price found for skinName", v.Name, "wear", wearStr)
			fmt.Println(data[v.Collection][v.Rarity])
			return types.TradeupOutput{}, localCache, "no price found for skinName"
		}
		inputWorth += price
	}
	inputWorth = roundFloat2(inputWorth)

	// --- Average float ---
	sum := 0.0
	for _, v := range inputSkins {
		sum += v.Wear
	}
	avgFloat := roundFloat2(sum/10.0)

	// --- Output skins ---
	inputCollectionCounts := make(map[string]int)
	for _, v := range inputSkins {
		inputCollectionCounts[v.Collection]++
	}

	outputSkins := make([]types.OutputSkin, 0, 6)
	for collection := range inputCollectionCounts {
		rarityMap, ok := data[collection][tradeupOutcomeRarity]
		if !ok {
			return types.TradeupOutput{}, localCache, "no outcome skins found for collection Rarity"
		}

		for skin, skinData := range rarityMap {
			if (skin[:4] == "Stat") != stattrak {
				continue
			}

			minFloat := skinData.WearRange[0]
			maxFloat := skinData.WearRange[1]
			outcomeFloat := roundFloat4((maxFloat-minFloat)*avgFloat+minFloat)
			wearStr := WearFloatToStr(outcomeFloat)

			key := types.OutputSkinKey{
				Name:       skin,
				WearStr:    wearStr,
			}

			var cachedSkin types.OutputSkin

			cachedSkin, ok = localCache.LocalOutputSkinCache[key]
			if !ok {
				globalSkin, found := outputSkinCache.Load(key)
				if found {
					cachedSkin = globalSkin.(types.OutputSkin)
					if localCache.LocalOutputSkinCache == nil {
						localCache.LocalOutputSkinCache = make(map[types.OutputSkinKey]types.OutputSkin)
					}
					localCache.LocalOutputSkinCache[key] = cachedSkin
					ok = true // mark as found
				}
			}

			if ok {
				outputSkins = append(outputSkins, cachedSkin)
				continue
			}

			price := skinData.Prices[wearStr]
			if price == 0 {
				return types.TradeupOutput{}, localCache, "no price found for skinName"
			}

			newSkin := types.OutputSkin{
				Name:       skin,
				Wear:       outcomeFloat,
				Rarity:     tradeupOutcomeRarity,
				Collection: collection,
				Price:      price,
				Chance:     0.0,
			}

			// outputSkinCacheMutex.Lock()
			outputSkinCache.Store(key, newSkin)
			// outputSkinCacheMutex.Unlock()

			outputSkins = append(outputSkins, newSkin)
		}
	}

	// --- Compute chances ---
	ballots := 0
	for _, skin := range outputSkins {
		ballots += inputCollectionCounts[skin.Collection]
	}

	outputWorth := 0.0
	expectedNet := 0.0
	oddsToProfit := 0.0
	oddsToProfitFee := 0.0

	// Compute chance, output worth, net worth, and odds in one pass
	ballotsF := float64(ballots)
	for i := range outputSkins {
		outputSkins[i].Chance = float64(inputCollectionCounts[outputSkins[i].Collection]) / ballotsF

		price := outputSkins[i].Price
		if price == 0 {
			return types.TradeupOutput{}, localCache, "no price found for skinName"
		}

		outputWorth += price * outputSkins[i].Chance

		netPrice := calculateSteamNet(price)
		expectedNet += netPrice * outputSkins[i].Chance

		if price > inputWorth {
			oddsToProfit += outputSkins[i].Chance
		}
		if netPrice > inputWorth {
			oddsToProfitFee += outputSkins[i].Chance
		}
	}

	// Round metrics
	metrics := types.OutputMetrics{
		OddsToProfit:      roundFloat2(math.Max(0, math.Min(oddsToProfit*100, 100))),
		OddsToProfitFee:   roundFloat2(math.Max(0, math.Min(oddsToProfitFee*100, 100))),
		AverageFloat:      avgFloat,
		InputWorth:        inputWorth,
		OutputWorth:       roundFloat2(outputWorth),
		OutcomeWorthFees:  roundFloat2(expectedNet),
		Profit:            roundFloat2(outputWorth-inputWorth),
		ProfitFees:        roundFloat2(expectedNet-inputWorth),
		Profitability:     roundFloat2((outputWorth/inputWorth)*100),
		ProfitabilityFees: roundFloat2((expectedNet/inputWorth)*100),
	}

	// fmt.Println(metrics.ProfitabilityFees)

	return types.TradeupOutput{
		Skins:   outputSkins,
		Metrics: metrics,
	}, localCache, ""
}