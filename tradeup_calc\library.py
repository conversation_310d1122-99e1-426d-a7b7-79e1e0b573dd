import orjson
import json
from collections import Counter

rarities = ["Consumer", "Industrial", "Mil-Spec", "Restricted", "Classified", "Covert"]

def WearFloatToStr(wear: float) -> str:
    if 0 <= wear < 0.07000001:
        return "Factory New"
    elif 0.07000001 <= wear < 0.15000001:
        return "Minimal Wear"
    elif 0.15000001 <= wear < 0.38000001:
        return "Field-Tested"
    elif 0.38000001 <= wear < 0.45000001:
        return "Well-Worn"
    elif 0.45000001 <= wear <= 1000001:
        return "Battle-Scarred"
    else:
        return "Invalid wear value"
    
def calculateSteamNet(price):
    # Valve's cut
    V_raw = round(price / 11.5, 3)
    V = round(V_raw - (V_raw % 0.01), 2)  # round down to 2 digits
    V = max(V, 0.01)

    # Game developer cut
    G_raw = round(price / 23, 3)
    G = round(G_raw - (G_raw % 0.01), 2)  # round down to 2 digits
    G = max(G, 0.01)

    # Net amount received
    net = price - V - G
    return round(net - 0.01, 2)

def simulateTradeup(inputSkins, data, upPriceInput=False):

    inputtedStattrak = True if "StatTrak™" in inputSkins[0]["name"] else False
    inputtedCollections = []
    inputtedFloats = [skin["float"] for skin in inputSkins]
    inputtedRarity = inputSkins[0]["rarity"]

    outcomeRarity = rarities[rarities.index(inputtedRarity) + 1]
    averageFloat = sum(inputtedFloats) / len(inputtedFloats)


    for skin in inputSkins:
        inputtedCollections.append(skin["collection"])
        if upPriceInput == True:
            for price in data[skin["collection"]][skin["rarity"]][skin["name"]]["prices"]:
                data[skin["collection"]][skin["rarity"]][skin["name"]]["prices"][price] += 0.01

    inputtedCollections = Counter(inputtedCollections)

    for collection in inputtedCollections:
        if outcomeRarity not in data[collection] or not data[collection][outcomeRarity]:
            raise Exception(f"No '{outcomeRarity}' skins in collection: {collection}")

    outcomeSkins = []

    for collection in inputtedCollections:
        for rarity in data[collection]:
            if rarity == outcomeRarity:
                for skin in data[collection][rarity]:
                    
                    # basic check for stattrak
                    if inputtedStattrak and "StatTrak™" not in skin:
                        continue
                    if not inputtedStattrak and "StatTrak™" in skin:
                        continue


                    # https://www.reddit.com/r/GlobalOffensiveTrade/comments/3a57au/psa_float_values_and_tradeups/
                    """
                    y=range * x + min=(max-min)*x+min
                    x=average float value of the 10 skins used
                    y=float value of the skins received
                    min= minimum of the FV range of the skin received
                    range=max-min
                    """

                    # https://www.tradeupspy.com/tools/trade-up-guide
                    " outcome skin float = ((max.float-min.float) * avg.float) + min.float "


                    minFloat = data[collection][rarity][skin]["wearRange"][0]
                    maxFloat = data[collection][rarity][skin]["wearRange"][1]
                    averageFloat = averageFloat # why the fuck not


                    outcomeFloat = ( ( maxFloat - minFloat ) * averageFloat ) + minFloat
                    outcomeFloat = round(outcomeFloat, 4)
                    outcomeSkins.append({"name": skin, "collection": collection, "rarity": rarity, "wearRange": data[collection][rarity][skin]["wearRange"], "float": outcomeFloat, "wear": WearFloatToStr(outcomeFloat)})
                    # print(skin, collection, rarity, data[collection][rarity][skin]["wearRange"], outcomeFloat, WearFloatToStr(outcomeFloat))

    ballots = 0

    outcomeCollections = []
    for skin in outcomeSkins:
        outcomeCollections.append(skin["collection"])
    outcomeCollections = Counter(outcomeCollections)
    # print(outcomeCollections)

    inputWorth = 0
    for skin in inputSkins:
        ballots += outcomeCollections[skin["collection"]]
        skin["price"] = data[skin["collection"]][skin["rarity"]][skin["name"]]["prices"][WearFloatToStr(skin["float"])]
        # print(skin)
        inputWorth += skin["price"]

    # print("|")

    outputWorth = 0
    oddsToProfit = 0
    oddsToProfitFee = 0
    for skin in outcomeSkins:
        skin["chance"] = inputtedCollections[skin["collection"]] / ballots
        skin["price"] = data[skin["collection"]][skin["rarity"]][skin["name"]]["prices"][skin["wear"]]
        # print(skin)
        outputWorth += skin["price"]*skin["chance"]

        if skin["price"] > inputWorth:
            oddsToProfit += 1
        if calculateSteamNet(skin["price"]) > inputWorth:
            oddsToProfitFee += 1

    oddsToProfit = round((oddsToProfit / len(outcomeSkins)) * 100, 2)
    oddsToProfitFee = round((oddsToProfitFee / len(outcomeSkins)) * 100, 2)
    averageFloat = round(averageFloat, 4)
    inputWorth = round(inputWorth, 2)
    outputWorth = round(outputWorth, 2)
    outcomeWorthFees = round(calculateSteamNet(outputWorth), 2)
    profit = round(outputWorth - inputWorth, 2)
    profitFees = round(calculateSteamNet(outputWorth) - inputWorth, 2)
    profitability = round((outputWorth/inputWorth)*100, 2)
    profitabilityFees = round(((calculateSteamNet(outputWorth)/inputWorth)*100), 2)

    return {"averageFloat": averageFloat, "inputWorth": inputWorth, "outputWorth": outputWorth, "outcomeWorthFees": outcomeWorthFees, "profit": profit, "profitFees": profitFees, "profitability": profitability, "profitabilityFees": profitabilityFees, "oddsToProfit": oddsToProfit, "oddsToProfitFee": oddsToProfitFee, "outcomeSkins": outcomeSkins}