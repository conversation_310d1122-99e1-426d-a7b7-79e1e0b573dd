package types

/// Input types

type InputSkin struct {
	Name   string  `json:"skinName"`   // skin name
	Wear       float64 `json:"wear"`       // wear value
	Rarity     string  `json:"rarity"`     // rarity name
	Collection string  `json:"collection"`
}

type InputSkinList [10]InputSkin

/// Output types

type OutputSkin struct {
	Name   string  `json:"skinName"`   // skin name
	Wear       float64 `json:"wear"`       // wear value
	Rarity     string  `json:"rarity"`     // rarity name
	Collection string  `json:"collection"` // wear name -> price
	Price      float64 `json:"price"`      // price of skin
	Chance     float64 `json:"chance"`     // chance of skin
}

type OutputMetrics struct {
	OddsToProfit     float64 `json:"oddsToProfit"`     // odds to profit
	OddsToProfitFee  float64 `json:"oddsToProfitFee"`  // odds to profit with fees
	AverageFloat     float64 `json:"averageFloat"`     // average float of outcome skins
	InputWorth       float64 `json:"inputWorth"`       // worth of input skins
	OutputWorth      float64 `json:"outputWorth"`      // worth of output skins
	OutcomeWorthFees float64 `json:"outcomeWorthFees"` // worth of output skins with fees
	Profit           float64 `json:"profit"`           // profit
	ProfitFees       float64 `json:"profitFees"`       // profit with fees
	Profitability    float64 `json:"profitability"`    // profitability
	ProfitabilityFees float64 `json:"profitabilityFees"` // profitability with fees
}

type TradeupOutput struct {
	Skins    []OutputSkin `json:"skins"`    // list of output skins
	Metrics  OutputMetrics  `json:"metrics"`  // metrics
}

/// Navigation types

type Skin struct {
	WearRange [2]float64         `json:"wearRange"` // always 2 floats
	Wears     []string           `json:"wears"`     // list of wear names
	Prices    map[string]float64 `json:"prices"`    // wear name -> price
}

type Rarity map[string]Skin // map of skinName -> Skin

type Collection map[string]Rarity // map of rarityName -> Rarity

type AllCollections map[string]Collection // map of collectionName -> Collection

// cache

type OutputSkinKey struct {
	Name       string
	WearStr    string
}

type LocalCache struct {
	LocalOutputSkinCache map[OutputSkinKey]OutputSkin
}