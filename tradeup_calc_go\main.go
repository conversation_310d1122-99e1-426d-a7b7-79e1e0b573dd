package main

import (
	"encoding/json"
	"fmt"
	"os"

	tu "tradeup_calc/tradeup"
	"tradeup_calc/types"
)

func main() {
	// Example data - you'll need to populate these with actual values
	var data types.AllCollections

	

	// loading in today.json to populate data
	// Read the JSON file
	dataFile, err := os.ReadFile(`today.json`)
	if err != nil {
		fmt.Println("Error reading file:", err)
		return
	}

	// Unmarshal into AllCollections type
	if err := json.Unmarshal(dataFile, &data); err != nil {
		panic(err)
	}

	var localCache types.LocalCache
	var inputSkins [10]types.InputSkin
	stattrak := false

	for collectionName, collection := range data {
		for rarityName, rarity := range collection {
			for skinName, skinData := range rarity {
				for _, wear := range skinData.Wears { 
					inputSkins[0] = types.InputSkin{Name: skinName, Wear: wear, Rarity: rarityName, Collection: collectionName}
					result, updatedCache, errorMsg := tu.SimulateTradeup(data, localCache, inputSkins, stattrak)
					if errorMsg != "" {
						fmt.Printf("Error: %s\n", errorMsg)
						return
					}
					fmt.Printf("Tradeup result: %+v\n", result)
					fmt.Printf("Updated cache: %+v\n", updatedCache)
				}
			}
		}
	}
}