import json

# Load your input data
with open('out.json', 'r') as file:
    data = json.load(file)

# Rarity mapping
# rarity_map = {
#     "common": "Consumer/white",
#     "uncommon": "Industrial/light-blue",
#     "rare": "Mil-Spec/blue",
#     "mythical": "Restricted/purple",
#     "legendary": "Classified/pink",
#     "ancient": "Covert/red",
#     "immortal": "Contraband/orange"
# }

# Iterate over collections and modify each item
for collection in data.get("collections", {}).values():
    for rarity_group in collection.values():
        for item in rarity_group:
            # Add rarityCommonName
            rarity_key = item.get("skinRarity")
            if rarity_key in rarity_map:
                item["rarityCommonName"] = rarity_map[rarity_key]

            # Remove redundant 'price' field if present
            item.pop("price", None)  # Safe remove

# Save the updated data
with open('out3.json', 'w') as file:
    json.dump(data, file, indent=4)
